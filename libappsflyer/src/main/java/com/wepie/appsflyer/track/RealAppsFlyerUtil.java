package com.wepie.appsflyer.track;

import static com.wepie.appsflyer.track.RealAppsFlyerEvent.APP_START;
import static com.wepie.appsflyer.track.RealAppsFlyerEvent.LOGIN;
import static com.wepie.appsflyer.track.RealAppsFlyerEvent.OPEN_GIFT_DIALOG;
import static com.wepie.appsflyer.track.RealAppsFlyerEvent.OPEN_GIFT_DIALOG_IN_FIRST_DAY;
import static com.wepie.appsflyer.track.RealAppsFlyerEvent.REGISTER;

import android.app.Activity;
import android.content.Context;
import android.os.SystemClock;
import android.text.TextUtils;
import android.text.format.DateUtils;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import com.appsflyer.AppsFlyerConversionListener;
import com.appsflyer.AppsFlyerLib;
import com.appsflyer.AppsFlyerProperties;
import com.appsflyer.attribution.AppsFlyerRequestListener;
import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.BaseConfig;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.MainApi;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.UserService;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wpdd.WpDdUtil;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RealAppsFlyerUtil {
    private static final String PREF_KEY_AF_UID = "pref_key_appsflyer_uid";
    private static final String TAG = "AppsFlyer";
    private static final String AF_DEV_KEY = "DbdfZFnNqyyDPGTdC7XiNT";

    private static final String AF_STATUS = "af_status";
    private static final String AF_ORGANIC = "Organic";

    private static boolean isStart = false;

    public static void init(Context context, int loginUid) {
        HLog.d(TAG, HLog.USR, "init!");
        long start = SystemClock.elapsedRealtime();
        AppsFlyerConversionListener conversionListener = new AppsFlyerConversionListener() {
            @Override
            public void onConversionDataSuccess(Map<String, Object> conversionData) {
                HLog.d(TAG, HLog.USR, "onConversionDataSuccess");
                Object status = conversionData.get(AF_STATUS);
                if (!AF_ORGANIC.equals(status)) {
                    try {
                        String info = JsonUtil.toJsonString(conversionData);
                        long spent = SystemClock.elapsedRealtime() - start;
                        HLog.aliLog(AliNetLogUtil.PORT.install, AliNetLogUtil.TYPE.normal, "af_init_data[" + spent + "]: " + info);
                    } catch (Exception e) {
                        FLog.e(e);
                    }
                }
                for (Map.Entry<String, Object> entry : conversionData.entrySet()) {
                    HLog.d(TAG, "attribute: {} = {}", entry.getKey(), entry.getValue());
                }
            }

            @Override
            public void onConversionDataFail(String errorMessage) {
                HLog.d(TAG, "error getting conversion data:{}", errorMessage);
            }

            @Override
            public void onAppOpenAttribution(Map<String, String> conversionData) {
                for (String attrName : conversionData.keySet()) {
                    HLog.d(TAG, "attribute: {}={}", attrName, conversionData.get(attrName));
                }
            }

            @Override
            public void onAttributionFailure(String errorMessage) {
                HLog.d(TAG, "error onAttributionFailure : " + errorMessage);
            }
        };
        AppsFlyerLib appsFlyerLib = AppsFlyerLib.getInstance();
        appsFlyerLib.init(AF_DEV_KEY, conversionListener, context);
        if (loginUid > 0) {
            String uid = AppsFlyerProperties.getInstance().getString(AppsFlyerProperties.APP_USER_ID);
            if (TextUtils.isEmpty(uid)) {
                HLog.d(TAG, HLog.CLR, "afid get uid property empty");
                appsFlyerLib.setCustomerUserId(String.valueOf(loginUid));
            } else if (!String.valueOf(loginUid).equals(uid)) {
                HLog.d(TAG, HLog.CLR, "afid get uid property not equal: {}, {}", uid, loginUid);
                appsFlyerLib.setCustomerUserId(String.valueOf(loginUid));
            } else {
                HLog.d(TAG, HLog.CLR, "afid get uid property: {}", uid);
            }
        } else {
            HLog.d(TAG, HLog.CLR, "login uid: {}", loginUid);
        }
        BaseConfig config = LibBaseUtil.getBaseConfig();
        if (!LibBaseUtil.CHANNEL_DEFAULT.equals(config.channel)) {
            appsFlyerLib.setOutOfStore(config.channel);
            HLog.d(TAG, HLog.CLR, "setOutOfStore: {}", config.channel);
        }
        appsFlyerLib.setDebugLog(LibBaseUtil.buildDebug());
        appsFlyerLib.setCollectIMEI(false);
        appsFlyerLib.setCollectAndroidID(false);
    }

    private static void start() {
        HLog.d(TAG, HLog.USR, "start");
        isStart = true;
        AppsFlyerLib appsFlyerLib = AppsFlyerLib.getInstance();
        Map<String, Object> map = new ArrayMap<>(1);
        map.put("ta_distinct_id", ApiService.of(TrackApi.class).getDistinctId());
        map.put("app_region", ApiService.of(MainApi.class).getRegion());
        map.put("os", "Android");
        map.put("did", getAndroidId());
        appsFlyerLib.setAdditionalData(map);
        Context ctx = ActivityTaskManager.getInstance().getTopActivity();
        if (ctx == null) {
            ctx = LibBaseUtil.getApplication();
        }
        appsFlyerLib.start(ctx, AF_DEV_KEY, new AppsFlyerRequestListener() {
            @Override
            public void onSuccess() {
                HLog.d(TAG, HLog.USR, "start onSuccess!");
            }

            @Override
            public void onError(int i, @NonNull String s) {
                HLog.d(TAG, HLog.USR, "start onError! i={}, msg={}", i, s);
            }
        });
    }

    private static String androidId;

    private static String getAndroidId() {
        if (androidId == null) {
            androidId = WpDdUtil.getInstance().getAndroidId();
        }
        return androidId;
    }

    public static void login(boolean newUser, int uid, String source) {
        AppsFlyerLib.getInstance().setCustomerUserId(String.valueOf(uid));
        if (newUser) {
            trackEvent(REGISTER, "sns_type", source);
        }
        trackEvent(LOGIN, "sns_type", source);
    }

    public static String getAfId() {
        try {
            String afId = AppsFlyerLib.getInstance().getAppsFlyerUID(LibBaseUtil.getApplication());
            PrefUtil.getInstance().setString(PREF_KEY_AF_UID, afId);
            return afId;
        } catch (Exception ignored) {
        }
        return PrefUtil.getInstance().getString(PREF_KEY_AF_UID, "");
    }

    public static void trackOpenGiftDialog() {
        trackFirstEvent(OPEN_GIFT_DIALOG);

        String key = getPrefKey(OPEN_GIFT_DIALOG_IN_FIRST_DAY);
        boolean checked = PrefUtil.getInstance().getBoolean(key, false);
        if (!checked) {
            PrefUtil.getInstance().setBoolean(key, true);
            // 第一次打开弹窗的时候，是不是首日记一次即可。
            long diff = UserService.get().getLoginUser().getRegisterTime() - TimeUtil.getServerTime();
            boolean firstDay = diff > 0 && diff < DateUtils.DAY_IN_MILLIS;
            HLog.d(TAG, "track first event: event={}, firstDay={}", OPEN_GIFT_DIALOG_IN_FIRST_DAY, firstDay);
            if (firstDay) {
                trackEvent(OPEN_GIFT_DIALOG_IN_FIRST_DAY);
            }
        }
    }

    public static void trackFirstEvent(String event) {
        String key = getPrefKey(event);
        boolean checked = PrefUtil.getInstance().getBoolean(key, false);
        HLog.d(TAG, "track first event: event={}, checked={}", event, checked);
        if (!checked) {
            PrefUtil.getInstance().setBoolean(key, true);
            trackEvent(event);
        }
    }

    public static String getPrefKey(String event) {
        return "pref_af_" + event;
    }

    public static void trackEvent(String event, @NonNull String... params) {
        trackEvent(event, Arrays.asList(params));
    }

    public static void trackEvent(String event, @NonNull List<String> params) {
        if (LibBaseUtil.buildDebug()) {
            if (params.size() % 2 != 0) {
                throw new IllegalArgumentException("error apps flyer event params");
            }
        }
        if (!isStart) {
            start();
        }
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < params.size(); i += 2) {
            map.put(params.get(i), params.get(i + 1));
        }
        map.put("did", getAndroidId());
        AppsFlyerLib.getInstance().logEvent(LibBaseUtil.getApplication(), event, map);
    }

    public static void trackFcmToken(String token) {
        HLog.d(TAG, "trackFcmToken for AppsFlyer {}", token);
        AppsFlyerLib.getInstance().updateServerUninstallToken(LibBaseUtil.getApplication(), token);
    }

    public static boolean interceptUninstallToken(Map<String, String> msgData) {
        if (msgData.containsKey("af-uinstall-tracking")) {
            HLog.d(TAG, "Handle af remote message");
            return true;
        }
        return false;
    }


    private static final ActivityTaskManager.ActivityTaskListener trackListener = new ActivityTaskManager.ActivityTaskListener() {
        private final String androidId = WpDdUtil.getInstance().getAndroidId();

        @Override
        public void onForeground(@NonNull Activity activity) {
            super.onForeground(activity);
            trackEvent(APP_START, "android_id", androidId, "resume_from_background", String.valueOf(true));
        }
    };

    public static void trackAppStart() {
        String androidId = getAndroidId();
        trackEvent(APP_START, "android_id", androidId, "resume_from_background", String.valueOf(false));
        ActivityTaskManager.getInstance().registerActivityTaskListener(trackListener);
    }
}
              