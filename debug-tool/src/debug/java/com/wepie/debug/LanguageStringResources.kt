package com.wepie.debug

import android.content.res.Resources
import com.huiwan.component.activity.ResourceWrap

class LanguageStringResources(
    res: Resources,
    private val stringCache: Map<Int, String>,
    private val stringArrayCache: Map<Int, Array<String>>,
) : ResourceWrap(res) {

    @Throws(NotFoundException::class)
    override fun getText(id: Int): CharSequence {
        return stringCache[id] ?: super.getText(id)
    }

    @Throws(NotFoundException::class)
    override fun getString(id: Int): String {
        return stringCache[id] ?: super.getString(id)

    }

    @Throws(NotFoundException::class)
    override fun getString(id: Int, vararg formatArgs: Any?): String {
        val format = stringCache[id]
        if (format != null) {
            return String.format(format, *formatArgs)
        }
        return super.getString(id, *formatArgs)
    }

    override fun getText(id: Int, def: CharSequence?): CharSequence {
        return stringCache[id] ?: super.getText(id, def)
    }

    @Throws(NotFoundException::class)
    override fun getTextArray(id: Int): Array<CharSequence> {
        val array = stringArrayCache[id]
        if (array != null) {
            return Array<CharSequence>(array.size) { array[it] }
        }
        return super.getTextArray(id)
    }

    @Throws(NotFoundException::class)
    override fun getStringArray(id: Int): Array<String> {
        return stringArrayCache[id] ?: super.getStringArray(id)
    }
}