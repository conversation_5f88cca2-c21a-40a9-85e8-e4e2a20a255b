apply from: "../base_module.gradle"

android {
    buildFeatures {
        buildConfig true
    }
    defaultConfig {
        buildConfigField "String", "TRANSLATION_ID", "\"${rootProject.ext.TRANSLATION_ID}\""
    }
}

configurations.configureEach {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

dependencies {
    implementation(libs.lottie) {
        exclude module: 'support-v7'
    }
    implementation project(":lib:baseutil")
    implementation project(":component:activity")
    implementation project(path: ':lib:api')
    implementation project(path: ':lib:store')
    implementation project(path: ':lib:liblog')
    implementation libs.base.ui
    debugImplementation(libs.skynet.debugTools) {
        exclude group: "androidx.core", module: "core-ktx"
    }
    debugImplementation(libs.skynet.largeimage) {
        exclude group: "androidx.core", module: "core-ktx"
    }
    debugImplementation(libs.skynet.mooner) {
        exclude group: "androidx.core", module: "core-ktx"
    }
    implementation project(path: ':service:UserService')
    implementation libs.glide

    implementation 'com.iqiyi.xcrash:xcrash-android-lib:3.1.0'
    debugImplementation 'org.apache.commons:commons-text:1.14.0'
    testImplementation(libs.junit)
}

apply from: rootProject.file("gradle/base-composed.gradle")
