package com.huiwan.configservice.constentity

import android.text.TextUtils
import com.google.gson.annotations.SerializedName
import com.huiwan.configservice.editionentity.IVersion
import com.huiwan.configservice.model.PropItem

class JKHomeConfig : IVersion {
    @SerializedName("version_num")
    private var version = 0

    @SerializedName("md5_version")
    private var versionMD5 = ""

    @SerializedName("game_list")
    val gameList: List<JkHomeConfigItem> = emptyList()

    @SerializedName("region")
    private var region = ""

    @SerializedName("game_stats_page")
    val gameStatsConfig = GameStatsConfig()

    // grade ->CollectionGradeConfigItem
    @SerializedName("collection_grade_conf")
    val collectionGradeConfig: Map<Int, CollectionGradeConfigItem> = emptyMap()

    @SerializedName("jk_skin_prop_types")
    val jkSkinTypeIdList: List<Int> = listOf(
        PropItem.TYPE_JACKAROO_PIECE, PropItem.TYPE_JACKAROO_BOARD,
        PropItem.TYPE_JACKAROO_PLAY_CARD_ANIMATION, PropItem.TYPE_JACKAROO_KILL_ANIMATION,
        PropItem.TYPE_JACKAROO_EXCHANGE_ANIMATION
    )

    override fun getVersion(): Int = version

    override fun getVersionMD5(): String = versionMD5

    override fun getRegion(): String = region

    override fun isAvailable(): Boolean {
        return if (!TextUtils.isEmpty(versionMD5)) {
            true
        } else {
            version > 0
        }
    }

    fun getGameStatConfig(gameType: Int): TotalGameStatConfig {
        for (config in gameStatsConfig.totalGameStat) {
            if (config.gameType == gameType) {
                return config
            }
        }
        return TotalGameStatConfig()
    }

    fun getGameStatConfigList(gameType: Int): List<GameModeStatConfig> {
        return getGameStatConfig(gameType).gameModeStatList
    }

    fun getGameStatConfig(gameType: Int, gameMode: Int): GameModeStatConfig {
        for (modeConfig in getGameStatConfigList(gameType)) {
            if (modeConfig.gameMode == gameMode) {
                return modeConfig
            }
        }
        return GameModeStatConfig()
    }

    fun getCollectionGrade(grade: Int): CollectionGradeConfigItem {
        return collectionGradeConfig[grade] ?: CollectionGradeConfigItem()
    }
}

class GameStatsConfig {
    @SerializedName("total_stats")
    var totalGameStat: List<TotalGameStatConfig> = ArrayList()
}

class TotalGameStatConfig {
    @SerializedName("stats")
    var gameModeStatList: List<GameModeStatConfig> = emptyList()

    @SerializedName("game_type")
    var gameType: Int = 0

    @SerializedName("game_name")
    var gameName: String = ""

}

class GameModeStatConfig {

    @SerializedName("game_mode")
    var gameMode = 0

    @SerializedName("game_mode_text")
    var gameModeText = ""

    @SerializedName("text_color")
    var textColor = "#FFFFFF"

    @SerializedName("background")
    var modeBackground = ""

    @SerializedName("icon")
    var modeIcon = ""
}

class CocosLoadingAnim {
    @SerializedName("standard_svga")
    var standardSvga: String? = null

    @SerializedName("wide_svga")
    var wideSvga: String? = null
}

data class CollectionGradeConfigItem(
    /**
     * 等级名称 SS 、S 、A 、B 、C
     */
    @SerializedName("grade_name")
    val gradeName: String = "",
    /**
     * 等级图标
     */
    @SerializedName("grade_icon")
    val gradeIconUrl: String = "",
    /**
     * 等级背景，收藏册和个人弹窗上使用
     */
    @SerializedName("grade_background")
    val gradeBackgroundUrl: String = "",
    /**
     * 收藏进度条颜色
     */
    @SerializedName("progress_color")
    val progressColor: String = "",
)