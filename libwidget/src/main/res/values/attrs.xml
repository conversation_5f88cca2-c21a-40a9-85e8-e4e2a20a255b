<?xml version="1.0" encoding="utf-8"?>
<resources>

    <attr name="SendBtnBg" format="reference" />

    <declare-styleable name="IndicatorView">
        <attr name="dot_color_select" format="color" />
        <attr name="dot_color_empty" format="color" />
        <attr name="dot_radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CustomCircleImageView">
        <attr name="civ_border_width" format="dimension" />
        <attr name="civ_corner_radius" format="dimension" />
        <attr name="civ_border_color" format="color" />
        <attr name="civ_border_overlay" format="boolean" />
        <attr name="civ_fill_color" format="color" />
    </declare-styleable>

    <!--action bar 需要的一些属性-->
    <declare-styleable name="BaseWpActionBar">
        <!--是否有导航栏-->
        <attr name="has_action_bar" format="boolean"/>
        <!--自定义导航栏高度，否则用默认高度-->
        <attr name="action_bar_height" format="integer"/>
        <!--导航栏颜色，否则默认白色-->
        <attr name="action_bar_color" format="color"/>
        <!--导航栏图片，否则默认白色-->
        <attr name="action_bar_drawable" format="reference"/>
        <!--是否显示StatusView，需要全屏的View, 默认显示-->
        <attr name="show_status_view" format="boolean"/>
        <!--浅色按钮文字颜色，默认为深色-->
        <attr name="white_style" format="boolean"/>
        <!--浅色StatueView文字颜色，默认为深色-->
        <attr name="change_status_text_color" format="boolean"/>

        <attr name="action_bar_title_name" format="string"/>
        <attr name="has_back_icon" format="boolean"/>
        <attr name="force_white_status" format="boolean"/>
        <attr name="no_elevation" format="boolean" />
        <!--是否是新的组件化样式-->
        <attr name="is_component_style" format="boolean"/>
    </declare-styleable>

    <!--banner 需要的一些属性-->
    <declare-styleable name="ViewPagerLineIndicator">
        <attr name="selected_color" format="color"/>
        <attr name="not_selected_color" format="color"/>
        <attr name="indicator_margin" format="dimension"/>
        <attr name="line_width" format="dimension"/>
        <attr name="line_length" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="RoundedImageView">
        <attr name="riv_corner_radius" format="dimension" />
        <attr name="riv_corner_radius_top_left" format="dimension" />
        <attr name="riv_corner_radius_top_right" format="dimension" />
        <attr name="riv_corner_radius_bottom_left" format="dimension" />
        <attr name="riv_corner_radius_bottom_right" format="dimension" />
        <attr name="riv_border_width" format="dimension" />
        <attr name="riv_border_color" format="color" />
        <attr name="riv_mutate_background" format="boolean" />
        <attr name="riv_oval" format="boolean" />
        <attr name="android:scaleType" />
        <attr name="riv_tile_mode">
            <enum name="clamp" value="0" />
            <enum name="repeat" value="1" />
            <enum name="mirror" value="2" />
        </attr>
        <attr name="riv_tile_mode_x">
            <enum name="clamp" value="0" />
            <enum name="repeat" value="1" />
            <enum name="mirror" value="2" />
        </attr>
        <attr name="riv_tile_mode_y">
            <enum name="clamp" value="0" />
            <enum name="repeat" value="1" />
            <enum name="mirror" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="RotateTextView">
        <attr name="degree" format="integer" />
    </declare-styleable>

    <declare-styleable name="HWEffectTextView">
        <attr name="start_color" format="color" />
        <attr name="end_color" format="color"/>
        <attr name="stroke_color" format="color" />
        <attr name="stroke_width" format="integer"/>
        <attr name="stroke_offset" format="float"/>
        <attr name="star_percent" format="float"/>
        <attr name="end_percent" format="float"/>
    </declare-styleable>

    <declare-styleable name="CircleTimerProgressBar">
        <attr name="ctpb_progress_color" format="color" />
        <attr name="ctpb_progress_bg_color" format="color" />
        <attr name="ctpb_progress_width" format="dimension" />
        <attr name="ctpb_progress_start_angle" format="integer" />
    </declare-styleable>


    <declare-styleable name="FadingFrameLayout">
        <attr name="position">
            <enum name="start" value="1" />
            <enum name="top" value="2" />
            <enum name="end" value="3" />
            <enum name="bottom" value="4" />
        </attr>
        <attr name="fading_size" format="dimension" />
    </declare-styleable>

    <declare-styleable name="WpSwitch">
        <attr name="android:checked" format="boolean" />
        <attr name="android:thumb" format="reference" />
        <attr name="android:track" format="reference" />
    </declare-styleable>

    <declare-styleable name="BorderImageView">
        <attr name="borderWidth" format="dimension"/>
        <attr name="borderColor" format="color"/>
        <attr name="cornerRadius" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="FlowLayout">
        <attr name="flFlow" format="boolean"/>
        <attr name="flChildSpacing" format="enum|dimension">
            <enum name="auto" value="-65536"/>
        </attr>
        <attr name="flMinChildSpacing" format="dimension"/>
        <attr name="flChildSpacingForLastRow" format="enum|dimension">
            <enum name="auto" value="-65536"/>
            <enum name="align" value="-65537"/>
        </attr>
        <attr name="flRowSpacing" format="enum|dimension">
            <enum name="auto" value="-65536"/>
        </attr>
        <attr name="flMaxRows" format="integer"/>
        <attr name="flRowVerticalGravity" format="enum">
            <enum name="auto" value="-65536"/>
            <enum name="top" value="0x30"/>
            <enum name="center" value="0x10"/>
            <enum name="bottom" value="0x50"/>
        </attr>
        <attr name="android:gravity"/>
    </declare-styleable>

    <declare-styleable name="FadingEdgeView">
        <attr name="edge_position">
            <flag name="top" value="0x01"/>
            <flag name="bottom" value="0x02"/>
            <flag name="left" value="0x04"/>
            <flag name="right" value="0x08"/>
        </attr>

        <attr name="edge_width" format="dimension"/>
    </declare-styleable>

    <attr name="hint" format="string"/>

    <declare-styleable name="SearchBar">
        <attr name="hint"/>
    </declare-styleable>

    <declare-styleable name="CountWordEditText">
        <attr name="maxInputNum" format="integer"/>
        <attr name="minInputNum" format="integer"/>
        <attr name="maxEtHeight" format="dimension"/>
        <attr name="text" format="string"/>
        <attr name="textSize" format="dimension"/>
        <attr name="hint" />
        <attr name="maxLines" format="integer"/>
        <attr name="singleLine" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="StickTopLinearLayout">
        <attr name="collapseViewId" format="reference" />
        <attr name="scrollViewId" format="reference" />
    </declare-styleable>
</resources>