package com.huiwan.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.NestedScrollingChild3
import androidx.core.view.NestedScrollingChildHelper
import androidx.core.view.NestedScrollingParent3
import androidx.core.view.NestedScrollingParentHelper
import androidx.core.view.ViewCompat
import androidx.core.view.children
import kotlin.math.max
import kotlin.math.min

/**
 * [targetCollapseView] 会随着ParentLinear 滚动，会展开和收缩的view
 * [targetScrollView] 用于嵌套内部滚动视图，[targetCollapseView] 收起时，[targetScrollView]则会多展示相应高度
 *
 * 基本想法是
 * 1，外层的AppBar未收起时，向上滑动，先响应 外层appBar，若外层appbar 已经收起，则先让 [targetCollapseView] 收起，最后是自view自己滚动
 * 2， 向下滑动时，先子 view自己处理，再让 [targetCollapseView] 展开，最后是让外层appBar展开
 */
class StickTopLinearLayout @JvmOverloads constructor(
    context: Context,
    attributeSet: AttributeSet? = null
) :
    LinearLayout(context, attributeSet), NestedScrollingParent3, NestedScrollingChild3 {
    private val parentHelper = NestedScrollingParentHelper(this)
    private val childHelper = NestedScrollingChildHelper(this).apply {
        isNestedScrollingEnabled = true
    }
    private var collapseViewId = View.NO_ID
    private var scrollViewId = View.NO_ID
    private val targetCollapseView: ViewGroup? by lazy { findViewById(collapseViewId) }
    private val maxScrollDis: Int
        get() = targetCollapseView?.height ?: 0

    //外层AppBar是否完全展开
    var targetAppBarTotalExpand = true

    //外层AppBar是否完全收起
    var targetAppBarCollapse = false
    private val targetScrollView: View? by lazy { findViewById(scrollViewId) }

    init {
        context.theme.obtainStyledAttributes(attributeSet, R.styleable.StickTopLinearLayout, 0, 0)
            .apply {
                collapseViewId =
                    getResourceId(R.styleable.StickTopLinearLayout_collapseViewId, View.NO_ID)
                scrollViewId =
                    getResourceId(R.styleable.StickTopLinearLayout_scrollViewId, View.NO_ID)
            }
    }

    /**
     * 设置完后需要 requestLayout
     */
    fun setCollapseViewId(id: Int) {
        if (collapseViewId == id) {
            return
        }
        collapseViewId = id
    }

    /**
     * 设置完后需要 requestLayout
     */
    fun setScrollViewId(id: Int) {
        if (scrollViewId == id) {
            return
        }
        scrollViewId = id
    }

    private fun onNestedScrollInternal(
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        type: Int,
        consumed: IntArray
    ) {
        // 向上滑动处理 AppBar未完全收起时，优先让父级处理
        if (dyUnconsumed > 0 && !targetAppBarCollapse) {
            val parentConsumed = IntArray(2)
            dispatchNestedScroll(
                dxConsumed, dyConsumed,
                dxUnconsumed, dyUnconsumed,
                null,
                type,
                parentConsumed
            )
            val remainY = dyUnconsumed - parentConsumed[1]
            // 自己消费剩余部分
            val selfConsumedY = selfConsumeVertical(remainY)
            consumed[1] += parentConsumed[1] + selfConsumedY
        }
        // 向下滑动时  AppBar未完全展开时，优先自己处理
        else if (dyUnconsumed < 0 && !targetAppBarTotalExpand) {

            val selfConsumedY = selfConsumeVertical(dyUnconsumed)
            val parentConsumed = IntArray(2)
            dispatchNestedScroll(
                dxConsumed, dyConsumed + selfConsumedY,
                dxUnconsumed, dyUnconsumed - selfConsumedY,
                null,
                type,
                parentConsumed
            )
            consumed[1] += parentConsumed[1] + selfConsumedY
        } else {
            // AppBar已完全展开，直接自己处理
            val selfConsumedY = selfConsumeVertical(dyUnconsumed)
            consumed[1] += selfConsumedY
        }
    }

    override fun onStartNestedScroll(child: View, target: View, axes: Int, type: Int): Boolean {
        return (if (orientation == HORIZONTAL) (axes and ViewCompat.SCROLL_AXIS_HORIZONTAL) != 0 else (axes and ViewCompat.SCROLL_AXIS_VERTICAL) != 0)
    }

    override fun getNestedScrollAxes(): Int {
        return parentHelper.nestedScrollAxes
    }

    override fun onNestedScrollAccepted(child: View, target: View, axes: Int, type: Int) {
        parentHelper.onNestedScrollAccepted(child, target, axes, type)
        // 开始作为 child 向上通知要开始滚动
        startNestedScroll(axes, type)
    }

    override fun onStopNestedScroll(target: View, type: Int) {
        parentHelper.onStopNestedScroll(target, type)
        stopNestedScroll(type)
    }

    override fun onNestedScroll(
        target: View,
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        type: Int,
        consumed: IntArray
    ) {
        onNestedScrollInternal(dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type, consumed)
    }

    override fun onNestedScroll(
        target: View,
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        type: Int
    ) {
        onNestedScrollInternal(
            dxConsumed,
            dyConsumed,
            dxUnconsumed,
            dyUnconsumed,
            type,
            IntArray(2)
        )
    }

    override fun onNestedPreScroll(target: View, dx: Int, dy: Int, consumed: IntArray, type: Int) {

        // 向上滑动时AppBar未完全收起时，优先让父级处理
        if (dy > 0 && !targetAppBarCollapse) {
            val parentConsumed = IntArray(2)
            dispatchNestedPreScroll(dx, dy, parentConsumed, null, type)
            val remainY = dy - parentConsumed[1]
            // 自己消费剩余部分
            val selfConsumedY = selfConsumeVertical(remainY)
            consumed[1] += parentConsumed[1] + selfConsumedY
        }
        // 向下滑动时 AppBar未完全展开时，优先自己处理
        else if (dy < 0 && !targetAppBarTotalExpand) {
            val selfConsumedY = selfConsumeVertical(dy)
            val parentConsumed = IntArray(2)
            dispatchNestedPreScroll(dx, dy - selfConsumedY, parentConsumed, null, type)
            consumed[1] += selfConsumedY + parentConsumed[1]
        } else {
            val selfConsumedY = selfConsumeVertical(dy)
            consumed[1] += selfConsumedY
        }
    }

    /**
     * 消费垂直滚动 dy 可用的垂直滚动距离
     * @return 消费的垂直距离
     */
    private fun selfConsumeVertical(dy: Int): Int {
        if (dy == 0) return dy
        if (dy > 0 && scrollY < maxScrollDis) {
            val distance = min(scrollY + dy, maxScrollDis)
            val movY = distance - scrollY
            scrollBy(0, movY)
            return movY
        }
        if (dy < 0 && scrollY > 0) {
            val distance = max(scrollY + dy, 0)
            val movY = distance - scrollY
            scrollBy(0, movY)
            return movY
        }
        return 0
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        targetCollapseView?.measuredHeight?.let { collapsedViewHeight ->
            children.firstOrNull { it == targetScrollView }?.let {
                measureChild(
                    it,
                    widthMeasureSpec,
                    MeasureSpec.makeMeasureSpec(
                        it.measuredHeight + collapsedViewHeight,
                        MeasureSpec.EXACTLY
                    )
                )
            }
        }

    }

    //=======childScroll
    override fun startNestedScroll(axes: Int, type: Int): Boolean {
        return childHelper.startNestedScroll(axes, type)
    }

    override fun stopNestedScroll(type: Int) {
        childHelper.stopNestedScroll(type)
    }

    override fun hasNestedScrollingParent(type: Int): Boolean {
        return childHelper.hasNestedScrollingParent(type)
    }

    override fun dispatchNestedScroll(
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        offsetInWindow: IntArray?,
        type: Int,
        consumed: IntArray
    ) {
        childHelper.dispatchNestedScroll(
            dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed,
            offsetInWindow, type, consumed
        )
    }

    override fun dispatchNestedScroll(
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        offsetInWindow: IntArray?,
        type: Int
    ): Boolean {
        return childHelper.dispatchNestedScroll(
            dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed,
            offsetInWindow, type
        )
    }

    override fun dispatchNestedPreScroll(
        dx: Int,
        dy: Int,
        consumed: IntArray?,
        offsetInWindow: IntArray?,
        type: Int
    ): Boolean {
        return childHelper.dispatchNestedPreScroll(dx, dy, consumed, offsetInWindow, type)
    }

}