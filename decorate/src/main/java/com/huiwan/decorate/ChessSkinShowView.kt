package com.huiwan.decorate

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.children
import androidx.core.view.updateLayoutParams
import com.huiwan.anim.LifecycleAnimView
import com.huiwan.base.ktx.hide
import com.huiwan.base.ktx.visible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.util.URIUtil
import com.huiwan.base.util.UrlUtil
import com.huiwan.configservice.constentity.propextra.JackarooSkinExtra
import com.huiwan.configservice.model.PropItem
import com.huiwan.decorate.databinding.JackarooChessPicShowViewBinding
import com.huiwan.store.file.FileCacheName
import com.huiwan.store.file.FileConfig.getSourceFileFullPath
import com.wepie.download.DownloadUtil
import com.wepie.download.LifeDownloadCallback
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import java.io.File

class ChessSkinShowView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val binding =
        JackarooChessPicShowViewBinding.inflate(LayoutInflater.from(context), this)

    init {
        clipChildren = false
    }

    /**
     * @param forceShowStaticPic 是否强制显示静态图
     */
    fun bind(propItem: PropItem?, forceShowStaticPic: Boolean = false) {
        binding.bind(propItem, forceShowStaticPic)
    }

    private fun JackarooChessPicShowViewBinding.bind(
        propItem: PropItem?,
        forceShowStaticPic: Boolean = false
    ) {
        propItem?.let { config ->
            config.getExtraByType(JackarooSkinExtra::class.java)?.let {
                when {
                    forceShowStaticPic -> {
                        showNormalChessRs(config.mediaUrl)
                    }

                    it.isVap() -> {
                        playVap(jackarooChessAnimLay, it.previewResUrl, config.itemId)
                    }

                    it.isSVGA() -> {
                        showNormalChessRs(it.previewResUrl)
                    }

                    else -> showNormalChessRs(config.mediaUrl)
                }
            }
        } ?: run {
            showNormalChessRs(null)
        }
    }

    private fun JackarooChessPicShowViewBinding.showNormalChessRs(url: String?) {
        jackarooChessIv.visible()
        stopVapAnim()
        if (url.isNullOrEmpty()) {
            jackarooChessIv.setImageDrawable(ResUtil.getDrawable(R.drawable.default_chess_skin))
        } else {
            WpImageLoader.load(
                url, jackarooChessIv, ImageLoadInfo.newGameSkinInfo()
            )
        }
    }

    private fun stopVapAnim() {
        binding.jackarooChessAnimLay.children.forEach {
            if (it is LifecycleAnimView) {
                it.clearAll()
            }
        }
        binding.jackarooChessAnimLay.gone()
        binding.jackarooChessAnimLay.removeAllViews()
    }

    private fun playVap(jackarooChessAnimLay: FrameLayout, url: String, propId: Int) {
        binding.jackarooChessIv.hide()
        if (jackarooChessAnimLay.childCount == 1) {
            val v = jackarooChessAnimLay.getChildAt(0)
            if (v is LifecycleAnimView && v.tag == url) {
                //同一个动效，不更新
                return
            }
        }
        stopVapAnim()
        jackarooChessAnimLay.visible()
        val animView = LifecycleAnimView(context)
        jackarooChessAnimLay.addView(animView)
        animView.updateLayoutParams {
            width = LayoutParams.MATCH_PARENT
            height = LayoutParams.MATCH_PARENT
        }
        animView.tag = url
        val localPath = getLocalAnimUrlPath(url, propId, "vap_")
        playVapFromUrlWithLocalPath(animView, url, localPath)
    }

    private fun getLocalAnimUrlPath(animUrl: String, propId: Int, suffix: String): String {
        val defaultMd5 = propId.toString()
        val urlMd5 = UrlUtil.urlToMd5(animUrl, defaultMd5)
        return URIUtil.urlLocalPathWithMd5(
            urlMd5,
            getSourceFileFullPath(FileCacheName.PROP_PATH) + suffix
        )
    }

    private fun playVapFromUrlWithLocalPath(
        animView: LifecycleAnimView,
        url: String,
        localPath: String
    ) {
        val file = File(localPath)
        if (file.exists() && file.length() > 0) {
            animView.visible()
            animView.setLoop(Int.MAX_VALUE)
            animView.startPlay(file)
        } else {
            DownloadUtil.downloadFile(
                true,
                url,
                localPath,
                object : LifeDownloadCallback(animView) {
                    override fun onSuccess(url: String, path: String) {
                        super.onSuccess(url, path)
                        animView.visible()
                        animView.setLoop(Int.MAX_VALUE)
                        animView.startPlay(File(path))
                    }

                    override fun onFail(msg: String) {
                        super.onFail(msg)
                    }
                })
        }
    }
}