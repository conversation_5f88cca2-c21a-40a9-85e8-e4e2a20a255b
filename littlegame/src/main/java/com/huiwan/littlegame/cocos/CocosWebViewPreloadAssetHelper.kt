package com.huiwan.littlegame.cocos

import android.content.Context
import android.net.Uri
import android.webkit.WebResourceResponse
import androidx.webkit.WebViewAssetLoader
import com.huiwan.base.util.ToastUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.store.file.FileConfig
import com.wejoy.littlegame.ILittleGameApi
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.liblog.main.HLog
import com.wepie.webview.intercept.WebAutoCloseInputStream
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.Pattern
import kotlin.math.min

object CocosWebViewPreloadAssetHelper {

    //////////////////// 预加载相关
    private const val TAG = "CocosWebViewPreloadAssetHelper"
    // 文件目录
    private const val COCOS_PRELOAD_URLS_FILE_DIR = "cocos-preload-necessary-urls"
    // 预加载上限，一方面防止文件过大，另一方面是反正多了也预加载不过来，ludo撑死就预加载了80多条url
    private const val MAX_PRELOAD_URL_COUNT = 120
    private var curGameType = -1
    private var responseMap: ConcurrentHashMap<String, WebResourceResponse>? = null
    // 被成功预加载的资源数，即responseMap的size，由于取资源的时候用的是map.remove而不是get，所以还需要一个单独的字段记录预加载资源数量
    private var preloadSuccessUrlCount = 0
    private var preloadJob: Job? = null
    // url写入txt时顺便记录下来，下次可以直接尝试从里面拿而不用每次都走IO
    private val urlRecordMap = HashMap<Int, List<UrlRequestInfo>>()

    /**
     * 预加载指定游戏对应的部分url，只用gameType进行区分
     * 在startActivity前调用，在loadUrl前取消协程，这之间的空档可以用来预加载资源
     */
    fun preloadAsset(context: Context, gameType: Int) {
        if (!ApiService.of(ILittleGameApi::class.java).getPreloadCocosAssetEnabled()) {
            return
        }
        // 当前已经有一个相同gameType的协程在跑，可以直接return
        if (curGameType == gameType && preloadJob?.isActive == true) {
            return
        }
        ToastUtil.debugShow("start preload cocos asset")
        preloadSuccessUrlCount = 0
        release()
        val applicationContext = context.applicationContext
        // 直接起一个协程，读出url后开始加载，该url在存入的时候已经按照请求次数进行排序了
        preloadJob = CoroutineScope(Dispatchers.IO).launch {
            val webViewAssetLoader = WebViewAssetLoader.Builder()
                .addPathHandler("/", WebViewAssetLoader.InternalStoragePathHandler(applicationContext, applicationContext.filesDir))
                .build()
            curGameType = gameType
            responseMap = ConcurrentHashMap()
            // 以txt中的文件为准，若读出来是空的，则只预加载初始的url，就是网页本身的那个html文件
            readFromTxtAsUrlRequestInfoList(generatePreloadUrlFilePath()).ifEmpty {
                listOf(UrlRequestInfo(wrapUrl(getUnpackDir(gameType, true))))
            }.forEach {
                if (!isActive) {
                    // 协程取消
                    preloadSuccessUrlCount = responseMap?.size ?: 0
                    return@launch
                }
                interceptAsset(webViewAssetLoader, Uri.parse(it.url))?.let { response ->
                    responseMap?.put(it.url, response)
                }
            }
            preloadSuccessUrlCount = responseMap?.size ?: 0
            // 协程自然结束，预加载完毕
        }
    }

    fun release() {
        responseMap = null
        cancelPreloadCoroutineIfStillRunning()
    }

    /**
     * 埋点上报+本地文件更新
     * @param finishedUrlRequestsWhileLoading loading期间完成的url请求
     * @param preloadHitUrls 参与预加载且被命中的url
     */
    fun updateLocalFile(finishedUrlRequestsWhileLoading: Collection<String>, preloadHitUrls: Collection<String>) {
        // 去重
        val finishedUrlSet = finishedUrlRequestsWhileLoading.toSet()
        val preloadHitUrlSet = preloadHitUrls.toSet()
        if (ApiService.of(ILittleGameApi::class.java).getPreloadCocosAssetEnabled()) {
            release()
            if (curGameType != -1) {
                val map = mapOf(
                    "game_type" to curGameType.toString(),
                    "total_request_count" to finishedUrlSet.size.toString(),
                    "total_preload_count" to preloadSuccessUrlCount.toString(),
                    "preload_hit_count" to preloadHitUrlSet.size.toString(),
                    "preload_hit_rate" to String.format("%.3f",(preloadHitUrlSet.size.toFloat() / preloadSuccessUrlCount))
                )
                HLog.aliLog(AliNetLogUtil.PORT.performance, AliNetLogUtil.TYPE.normal, "preload_cocos_asset", map)
                CoroutineScope(Dispatchers.IO).launch {
                    mergeToTxt(generatePreloadUrlFilePath(), finishedUrlSet, preloadHitUrlSet)
                    curGameType = -1
                }
            }
        }
    }

    fun cancelPreloadCoroutineIfStillRunning() {
        preloadJob?.cancel()
        preloadJob = null
    }

    fun tryReadResponseFromPreload(url: String): WebResourceResponse? {
        return responseMap?.remove(url)
    }

    private fun interceptAsset(
        assetLoader: WebViewAssetLoader, url: Uri
    ): WebResourceResponse? {
        val rsp = assetLoader.shouldInterceptRequest(url) ?: return null
        try {
            rsp.data = WebAutoCloseInputStream.create(url.toString(), rsp.data)
            return rsp
        } catch (e: Exception) {
            return assetLoader.shouldInterceptRequest(url)
        }
    }

    private fun overwriteUrlRequestInfoListToTxt(path: String, content: List<UrlRequestInfo>) {
        if (content.isEmpty()) {
            return
        }
        // 写入的url数量不超过设定上限
        val subList = content.subList(0, min(content.size, MAX_PRELOAD_URL_COUNT))
        subList.sortedByDescending {
            it.requestTimes
        }.also {
            urlRecordMap[curGameType] = it
        }.map {
            it.toLine()
        }.let {
            overwriteToTxt(path, it)
        }
    }

    private fun overwriteToTxt(path: String, content: List<String>) {
        if (content.isEmpty()) {
            return
        }
        try {
            val file = File(path)
            file.parentFile?.mkdirs()
            file.writeText("")
            file.bufferedWriter().use { writer ->
                content.forEachIndexed { index, line ->
                    writer.append(line)
                    if (index < content.size - 1) {
                        writer.newLine()
                    }
                }
            }
        } catch (e: Exception) {
            HLog.d(TAG, e.message)
        }
    }

    /**
     * @param forceIO 强制通过IO获取，如果为false则尝试从上一次的记录中获取，获取不到再走IO
     */
    private fun readFromTxtAsUrlRequestInfoList(path: String, forceIO: Boolean = false): List<UrlRequestInfo> {
        return if (forceIO) {
            readFromTxtAsList(path).mapNotNull {
                UrlRequestInfo.fromLine(it)
            }
        } else {
            urlRecordMap[curGameType]?.let {
                return it
            }
            readFromTxtAsList(path).mapNotNull {
                UrlRequestInfo.fromLine(it)
            }.also {
                urlRecordMap[curGameType] = it
            }
        }
    }

    private fun readFromTxtAsList(path: String): List<String> {
        return try {
            File(path).takeIf { it.exists() }?.readLines() ?: emptyList()
        } catch (e: Exception) {
            HLog.d(TAG, e.message)
            emptyList()
        }
    }

    /**
     * 新旧数据合并
     * 1. 新数据有 + 旧数据有   => [请求次数]+1后写入
     * 2. 新数据有 + 旧数据没有  => 写入，[请求次数]=1
     * 3. 新数据没有 + 旧数据有  => 移除
     * [预加载命中次数]按实际情况+1就行
     */
    private fun mergeToTxt(path: String, newUrlSet: Set<String>, preloadHitUrlSet: Set<String>) {
        val oldData = readFromTxtAsUrlRequestInfoList(path)
        val oldDataMap = oldData.associateBy { it.url }
        val newResultData = oldData.toMutableList()
        newUrlSet.forEach { newUrl ->
            oldDataMap[newUrl]?.let {
                // 情况1
                it.requestTimes++
            } ?: run {
                // 情况2
                newResultData.add(UrlRequestInfo(newUrl))
            }
        }
        // 情况3
        for (i in newResultData.size - 1 downTo 0) {
            if (!newUrlSet.contains(newResultData[i].url)) {
                newResultData.removeAt(i)
            }
        }
        // 更新预加载命中次数
        newResultData.forEach {
            if (preloadHitUrlSet.contains(it.url)) {
                it.preloadHitTimes ++
            }
        }
        overwriteUrlRequestInfoListToTxt(path, newResultData)
    }

    /**
     * 获取指定gameType所需的预加载内容的文件路径
     */
    private fun generatePreloadUrlFilePath(gameType: Int = curGameType): String {
        return FileConfig.getExtraFolderPath() +
                COCOS_PRELOAD_URLS_FILE_DIR +
                File.separator +
                "preload-url-${gameType}.txt"
    }
}

/**
 * 本地文件格式
 * [url][请求次数][预加载命中次数]
 * @param url url
 * @param requestTimes 总请求次数
 * @param preloadHitTimes 预加载命中次数（被预加载且被web使用）
 */
data class UrlRequestInfo(
    val url: String,
    var requestTimes: Int = 1,
    var preloadHitTimes: Int = 0
) {
    companion object {
        fun fromLine(line: String): UrlRequestInfo? {
            Pattern.compile("""\[(.*?)]\[(\d+)]\[(\d+)]""").matcher(line).let {
                if (it.matches()) {
                    return UrlRequestInfo(it.group(1)!!, it.group(2)!!.toInt(), it.group(3)!!.toInt())
                }
            }
            // 兼容一下之前的文件格式
            Pattern.compile("""\[(.*?)]\[(\d+)]""").matcher(line).let {
                if (it.matches()) {
                    return UrlRequestInfo(it.group(1)!!, it.group(2)!!.toInt(), 0)
                }
            }
            return null
        }
    }

    fun toLine() = "[$url][$requestTimes][$preloadHitTimes]"
}