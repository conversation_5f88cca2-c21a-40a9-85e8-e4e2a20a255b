package com.huiwan.littlegame.cocos

import android.graphics.BitmapFactory
import android.util.Base64
import com.github.lzyzsd.jsbridge.CallBackFunction
import com.google.gson.JsonObject
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.BitmapUtil
import com.huiwan.base.util.GlobalEventFlow
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.SecurityUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.getArray
import com.huiwan.base.util.getBoolean
import com.huiwan.base.util.getFloat
import com.huiwan.base.util.getInt
import com.huiwan.base.util.getObject
import com.huiwan.base.util.getString
import com.huiwan.base.util.toList
import com.huiwan.component.gift.show.anim.AnimaRect
import com.huiwan.component.gift.show.anim.PropAnimInfo
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.international.regoin.IDRegionUtil.getFinalIDStrByGameType
import com.huiwan.configservice.international.service.GlobalConfigManager
import com.huiwan.lib.api.DataCallback
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.IapApi
import com.huiwan.lib.api.plugins.JumpApi
import com.huiwan.lib.api.plugins.friend.AddFriendApi
import com.huiwan.lib.api.plugins.friend.AddFriendCallback
import com.huiwan.lib.api.plugins.friend.AddFriendInfo
import com.huiwan.littlegame.EventDispatcher
import com.huiwan.littlegame.Js2JavaCommon
import com.huiwan.littlegame.bridge.CocosDataUtil
import com.huiwan.littlegame.bridge.CocosDataUtil.formUserInfoData
import com.huiwan.littlegame.cocos.apis.BaseCocosApi
import com.huiwan.littlegame.cocos.apis.CocosApiClearDialog
import com.huiwan.littlegame.cocos.apis.CocosApiGameStateChanged
import com.huiwan.littlegame.cocos.apis.CocosApiGetLoadData
import com.huiwan.littlegame.cocos.apis.CocosApiInviteDialog
import com.huiwan.littlegame.cocos.apis.CocosApiWriteLog
import com.huiwan.littlegame.cocos.apis.CocosApiOpenDeepLink
import com.huiwan.littlegame.cocos.apis.CocosApiRelaunch
import com.huiwan.littlegame.cocos.apis.CocosApiShowInputView
import com.huiwan.littlegame.cocos.apis.CocosApiShowInputView2
import com.huiwan.littlegame.cocos.apis.CocosApiShowInputView3
import com.huiwan.littlegame.cocos.apis.CocosApiShowNotEnoughAlert
import com.huiwan.littlegame.cocos.apis.CocosApiShowPlatGiftDialog
import com.huiwan.littlegame.cocos.apis.CocosApiShowUserDialog
import com.huiwan.littlegame.cocos.apis.CocosExitChannel
import com.huiwan.littlegame.cocos.apis.CocosInnerPurchase
import com.huiwan.littlegame.cocos.apis.CocosJoinChannel
import com.huiwan.littlegame.cocos.apis.CocosMuteStream
import com.huiwan.littlegame.cocos.apis.CocosRequestAudioPermission
import com.huiwan.littlegame.cocos.apis.CocosToggleMic
import com.huiwan.littlegame.cocos.apis.CocosToggleSound
import com.huiwan.littlegame.cocos.apis.GetAssetBundleInfo
import com.huiwan.littlegame.cocos.apis.GetMiniGameStartTime
import com.huiwan.littlegame.cocos.apis.LoadABBundle
import com.huiwan.littlegame.cocos.apis.ReloadAssetBundle
import com.huiwan.littlegame.event.CocosGameReturnForRematchEvent
import com.huiwan.littlegame.event.CocosMainActivityFinishEvent
import com.huiwan.littlegame.event.CocosRestartMatchEvent
import com.huiwan.littlegame.event.HideCocosGameSendViewEvent
import com.huiwan.littlegame.event.IceGameCommandEvent
import com.huiwan.littlegame.model.GameChessboardRes
import com.huiwan.littlegame.util.CocosSoundUtil
import com.huiwan.littlegame.util.CommonUtil
import com.huiwan.littlegame.util.LittleGameUtil
import com.huiwan.media.VolumeUtil
import com.huiwan.platform.ThreadUtil
import com.huiwan.store.PrefUtil
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserInfoLoadCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.User
import com.huiwan.voiceservice.VoiceManager
import com.wejoy.littlegame.LittleGame
import com.wejoy.weplay.ex.context.toActivity
import com.wejoy.weplay.ex.view.toLife
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog
import com.wepie.libpermission.PermissionCallback
import org.greenrobot.eventbus.EventBus
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

object CocosBridgeInterface {
    private const val TAG = "CocosBridgeInterface"
    internal var jsbConfig = CocosWebViewJsbConfig()
    private fun formJsonDataNoEncode(code: Int, msg: String?, jo: JSONObject?): String {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("code", code)
            jsonObject.put("msg", msg)
            jsonObject.put("data", jo)
        } catch (e: JSONException) {
            logJsonErr(e)
        }
        return jsonObject.toString()
    }

    private fun formJsonDataNoEncode(code: Int, msg: String?, seq: Int, jo: JSONObject?): String {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("code", code)
            jsonObject.put("msg", msg)
            jsonObject.put("seq", seq)
            jsonObject.put("data", jo)
        } catch (e: JSONException) {
            logJsonErr(e)
        }
        return jsonObject.toString()
    }

    /**
     * 简单的无须特别返回值的 rsp.
     */
    private fun okSimpleRsp(): String {
        return "{\"code\":200, \"msg\":\"\", \"data\":{}}"
    }

    private fun errRsp(msg: String?): String {
        val jsonObject = JSONObject()
        try {
            jsonObject.put("code", 500)
            jsonObject.put("msg", msg ?: "unknown")
            jsonObject.put("data", JSONObject())
        } catch (e: JSONException) {
            FLog.e(e)
        }
        return jsonObject.toString()
    }

    /**
     * 更新该类的时候，需要检查文档，是否更新。
     * https://wepie.feishu.cn/wiki/Q5wZw9l5ViWBFMkuFpuc2uLlnVe
     */
    @JvmStatic
    fun registerCocos(wpWebView: CocosWebView) {
        wpWebView.registerHandler("GetSelfUid") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("uid", LoginHelper.getLoginUid())
            } catch (e: JSONException) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetSelfUid success: {}", result)
        }
        wpWebView.registerHandler("GetUserInfo") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JSONObject(data)
                val uid = if (jsonObject.has("uid")) jsonObject.getInt("uid") else -1
                val seq = if (jsonObject.has("seq")) jsonObject.getInt("seq") else -1
                UserService.get().getCacheUserFromServer(uid, object : UserInfoLoadCallback {
                    override fun onUserInfoSuccess(info: User) {
                        try {
                            val result = formUserInfoData(info, seq)
                            invokeCallback(function, result)
                            fileLog("GetUserInfo success: {}", result)
                        } catch (e: Exception) {
                            fileLog("GetUserInfo failed: {}", e)
                            invokeCallback(function, CocosDataUtil.formError(500, e.message))
                        }
                    }

                    override fun onUserInfoFailed(description: String) {
                        ToastUtil.debugShow(description)
                        fileLog("GetUserInfo failed: {}", description)
                        invokeCallback(function, CocosDataUtil.formError(500, description))
                    }
                })
            } catch (e: Exception) {
                fileLog("GetUserInfo failed: {}", e)
            }
        }
        wpWebView.registerHandler("HideLoadingView") { data: String, function: CallBackFunction ->
            EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_STOP_LOADING)
            wpWebView.setIsLoading(false)
            invokeCallback(function, okSimpleRsp())
            cocosTrackEnter(wpWebView.gameInfo.gameType)
            fileLog("HideLoadingView success")
        }
        wpWebView.registerHandler("GetVersionCode") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("version_code", LibBaseUtil.getBaseConfig().versionCode)
            } catch (ignored: JSONException) {
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetVersionCode success: {}", result)
        }
        wpWebView.registerHandler("GetServerHost") { data: String, function: CallBackFunction ->
            val config = GlobalConfigManager.getInstance().configs
            val result: String? = try {
                formJsonDataNoEncode(200, "", JSONObject(config ?: ""))
            } catch (e: Exception) {
                FLog.e(e)
                CocosDataUtil.formError(500, e.message)
            }
            invokeCallback(function, result)
            fileLog("GetServerHost success: {}", config)
        }
        wpWebView.registerHandler("HandleGameRid") { data: String, function: CallBackFunction ->
            val res = JSONObject()
            try {
                val jsonObject = JSONObject(data)
                val rid = jsonObject.optInt("rid", 0)
                val gameType = jsonObject.optInt("game_type", 0)
                val id = getFinalIDStrByGameType(rid.toLong(), gameType)
                res.put("rid", id)
            } catch (e: Exception) {
                fileLog("HandleGameRid,data={}, error={}", data, e)
            }
            val result = formJsonDataNoEncode(200, "", res)
            invokeCallback(function, result)
            fileLog("HandleGameRid success: {}", result)
        }
        wpWebView.registerHandler("CocosCaptureScreen") { data: String, function: CallBackFunction ->
            invokeCallback(function, okSimpleRsp())
            fileLog("CocosCaptureScreen success")
        }
        wpWebView.registerHandler("ImageBase64") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JSONObject(data)
                val base64 = jsonObject.getString("base").substring(21)
                val decodedByte = Base64.decode(base64, 0)
                val bitmap = BitmapFactory.decodeByteArray(decodedByte, 0, decodedByte.size)
                BitmapUtil.saveBitmap(bitmap, File(wpWebView.path + "game.png"))
                invokeCallback(function, okSimpleRsp())
                fileLog("ImageBase64 success")
            } catch (e: Exception) {
                fileLog("ImageBase64 failed: {}", e)
            }
        }
        wpWebView.registerHandler("jsBackPress") { data: String, function: CallBackFunction ->
            invokeCallback(function, okSimpleRsp())
            fileLog("jsBackPress success")
        }
        wpWebView.registerHandler("jsBeforeEndDirector") { data: String, function: CallBackFunction ->
            invokeCallback(function, okSimpleRsp())
            fileLog("jsBeforeEndDirector success")
        }
        wpWebView.registerHandler("GetRid") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("rid", wpWebView.launchInfo.rid)
            } catch (e: JSONException) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetRid@{} success: {}", wpWebView.hashCode(), result)
        }
        wpWebView.registerHandler("GetVerifyKey") { data: String, function: CallBackFunction ->
            val game_type = wpWebView.gameInfo.gameType
            val pStr = game_type.toString() + "_" + CommonUtil.getSid()
            val jsonObject = JSONObject()
            try {
                jsonObject.put("verify_key", SecurityUtil.md5String(pStr))
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetVerifyKey success: {}", result)
        }
        wpWebView.registerHandler("showSendGiftDialog") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JsonUtil.toObject(data)
                val uid = jsonObject.getInt("uid")

                val subSource: String = jsonObject.getString("sub_source", "")
                val giftId: Int = jsonObject.getInt("giftId", 0)
                val uidList = JsonUtil.getArray(jsonObject, "uidList").toList(Int::class.java)
                val isMultiSend = jsonObject.getBoolean("isMultiSend", true)
                val rid = jsonObject.getInt("rid")
                val notifyUid = JsonUtil.getArray(jsonObject, "notify_uids").toList(Int::class.java)
                val event = IceGameCommandEvent(IceGameCommandEvent.COMMAND_SHOW_SEND_GIFT_DIALOG)
                event.subSource = subSource
                event.giftId = giftId
                event.rid = rid
                if (uidList.isNotEmpty()) {
                    event.isMultiSend = isMultiSend
                    event.gamerList = uidList
                    event.notifyUidList = notifyUid
                } else {
                    event.uid = uid
                    event.notifyUidList = notifyUid
                }
                EventDispatcher.postIceCommand(event)
                invokeCallback(function, okSimpleRsp())
                fileLog("showSendGiftDialog success")
            } catch (e: Exception) {
                val result = CocosDataUtil.formError(500, e.message)
                invokeCallback(function, result)
                fileLog("showSendGiftDialog failed: {}, {}", result, e)
            }
        }

        wpWebView.registerHandler("playPropAnimation") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JsonUtil.toObject(data)
                val propId = jsonObject.getInt("propId")
                val rect = jsonObject.getObject("rect")
                val downloadLimitTime = jsonObject.getInt("download_limit_time")
                val funcId = jsonObject.getString("funcId")
                val animaRect = parseAnimRectInfo(rect)
                val propAnimaInfo = PropAnimInfo(downloadLimitTime, funcId, animaRect)
                EventDispatcher.postIceCommand(
                    IceGameCommandEvent.COMMAND_PLAY_PROP_ANIMATION,
                    propId,
                    propAnimaInfo
                )
                invokeCallback(function, okSimpleRsp())
                fileLog("playPropAnimation success")
            } catch (e: Exception) {
                val result = CocosDataUtil.formError(500, e.message)
                invokeCallback(function, result)
                fileLog("playPropAnimation failed: {}, {}", result, e)
            }
        }

        wpWebView.registerHandler("IsFriend") { data: String, function: CallBackFunction ->
            try {
                val json = JSONObject(data)
                val uid = json.getInt("uid")
                val isFriendAto = AtomicBoolean(false)
                val latch = CountDownLatch(1)
                AddFriendApi::class.impl().isFriend(uid, object : DataCallback<Boolean> {
                    override fun onCall(data: Boolean) {
                        isFriendAto.set(data)
                        latch.countDown()
                    }

                    override fun onFailed(code: Int, msg: String) {
                        latch.countDown()
                    }
                })
                latch.await(100, TimeUnit.MILLISECONDS)
                val jsonObject = JSONObject()
                try {
                    jsonObject.put("is_friend", isFriendAto.get())
                } catch (e: JSONException) {
                    logJsonErr(e)
                }
                val result = formJsonDataNoEncode(200, "", jsonObject)
                invokeCallback(function, result)
                fileLog("isFriend success: {}", result)
            } catch (e: Exception) {
                invokeCallback(function, CocosDataUtil.formError(200, e.message))
                fileLog("isFriend failed: {}", e)
            }
        }
        wpWebView.registerHandler("AddFriend") { data: String, function: CallBackFunction ->
            try {
                val json = JSONObject(data)
                val uid = json.getInt("uid")
                var subSource: String? = ""
                if (json.has("sub_source")) {
                    subSource = json.getString("sub_source")
                }
                val gameType = wpWebView.gameInfo.gameType
                val addFriendInfo = AddFriendInfo(uid, gameType)
                    .setSource(TrackUtil.getGameTypeSource(gameType))
                    .setSubSource(subSource)
                if (LittleGame.gameInfo.isVoiceGame) {
                    addFriendInfo.scene = AddFriendInfo.SCENE_VOICE_ROOM
                } else {
                    addFriendInfo.scene = AddFriendInfo.SCENE_GAME
                }
                val trackExtData = addFriendInfo.trackExtData
                wpWebView.gameInfo.addTrackInfo(trackExtData)

                CommonUtil.addFriendApi(
                    wpWebView.context,
                    addFriendInfo,
                    object : AddFriendCallback {
                        override fun onSuccess(msg: String) {
                            ToastUtil.show(msg)
                            EventBus.getDefault().post(HideCocosGameSendViewEvent())
                            try {
                                val res = JSONObject()
                                res.put("addSuccess", true)
                                val result = formJsonDataNoEncode(200, "", res)
                                invokeCallback(function, result)
                                fileLog("AddFriend success: {}", result)
                            } catch (e: JSONException) {
                                fileLog("AddFriend failed: {}", e)
                            }
                        }

                        override fun onFail(code: Int, msg: String) {
                            ToastUtil.show(msg)
                            EventBus.getDefault().post(HideCocosGameSendViewEvent())
                            try {
                                val res = JSONObject()
                                res.put("addSuccess", false)
                                val result = formJsonDataNoEncode(200, "", res)
                                invokeCallback(function, result)
                                fileLog("AddFriend success: {}", result)
                            } catch (e: JSONException) {
                                fileLog("AddFriend failed: {}", e)
                            }
                        }
                    })
            } catch (e: JSONException) {
                invokeCallback(function, CocosDataUtil.formError(500, e.message))
                fileLog("AddFriend failed: {}", e)
            }
        }
        wpWebView.registerHandler("clickShare") { data: String, function: CallBackFunction ->
            clickShare("{}")
            invokeCallback(function, okSimpleRsp())
            fileLog("clickShare success")
        }
        wpWebView.registerHandler("ShareGame") { data: String, function: CallBackFunction ->
            if (data.isEmpty()) {
                clickShare("{}")
            } else {
                clickShare(data)
            }
            invokeCallback(function, okSimpleRsp())
            fileLog("ShareGame success")
        }
        wpWebView.registerHandler("Share") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JSONObject(data)
                val type = jsonObject.optInt("type")
                val title = jsonObject.optString("title")
                val desc = jsonObject.optString("desc")
                val link = jsonObject.optString("link")
                val iconUrl = jsonObject.optString("icon_url")
                EventDispatcher.doCocosShare(type, title, desc, link, iconUrl)
                invokeCallback(function, okSimpleRsp())
                fileLog("Share success")
            } catch (e: Exception) {
                fileLog("Share failed: {}", e)
            }
        }
        wpWebView.registerHandler("GameReturn") { data: String, function: CallBackFunction ->
            //游戏通知马上要回收资源了，不在请求jsb
            fileLog("GameReturn ${data}")
            CocosSoundUtil.releaseAll()
            var gameType = 0
            var cid = 0
            try {
                val json = JSONObject(data)
                gameType = json.getInt("game_type")
                cid = json.getInt("cid")
            } catch (e: java.lang.Exception) {
                fileLog("GameReturn parse message failed: {}", e)
            }
            if (gameType > 0 && cid > 0) {
                EventDispatcher.postBackCompetition(gameType, cid)
            }
            EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_GAME_RETURN)
            invokeCallback(function, okSimpleRsp())
            fileLog("GameReturn success")
        }

        wpWebView.registerHandler("CloseGame") { data: String, function: CallBackFunction ->
            fileLog("CloseGame")
            try {
                val json = JSONObject(data)
                val reason = json.getInt("reason")
                if (reason == 1) {
                    GlobalEventFlow.postEvent(CocosGameReturnForRematchEvent())
                }
            } catch (e: java.lang.Exception) {
                fileLog("CloseGame parse message failed: {}", e)
            }
            CocosSoundUtil.releaseAll()
            EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_GAME_RETURN)
            invokeCallback(function, okSimpleRsp())
            fileLog("CloseGame success")
        }
        wpWebView.registerHandler("GameReturnAndJump") { data: String, function: CallBackFunction ->
            Js2JavaCommon.closeGame()
            try {
                val json = JSONObject(data)
                val deeplink = json.getString("deeplink")
                jumpDeeplink(deeplink, false)
                invokeCallback(function, okSimpleRsp())
                fileLog("GameReturnAndJump success")
            } catch (e: java.lang.Exception) {
                invokeCallback(function, CocosDataUtil.formError(500, e.message))
                fileLog("GameReturnAndJump failed: {}", e)
            }
        }
        wpWebView.registerHandler("GetWatcher") { data: String, function: CallBackFunction ->
            val result = Js2JavaCommon.GetWatcher()
            invokeCallback(function, result)
            fileLog("GetWatcher success: {}", result)
        }
        wpWebView.registerHandler("SetAudioVolumeIndication") { data: String, function: CallBackFunction ->
            var interval = 1000
            var smooth = 3
            try {
                val json = JSONObject(data)
                if (json.has("interval")) interval = json.getInt("interval")
                if (json.has("smooth")) smooth = json.getInt("smooth")
                CommonUtil.setAudioVolumeIndication(interval, smooth)
                invokeCallback(function, okSimpleRsp())
                fileLog("SetAudioVolumeIndication success")
            } catch (e: JSONException) {
                invokeCallback(function, CocosDataUtil.formError(500, e.message))
                fileLog("SetAudioVolumeIndication failed: {}", e)
            }
        }
        wpWebView.registerHandler("TouchMic") { data: String, function: CallBackFunction ->
            val activity = wpWebView.context.toActivity() ?: return@registerHandler
            fileLog("TouchMic {}", data)
            requestGameAudioPermission(activity, object : PermissionCallback {
                override fun hasPermission(
                    granted: MutableList<String>?,
                    isAll: Boolean,
                    alreadyHas: Boolean
                ) {
                    if (alreadyHas) {
                        // 由于权限关闭导致的界面内展示为关闭状态。
                        val result = Js2JavaCommon.TouchMic()
                        invokeCallback(function, result)
                        fileLog("TouchMic success: {}", result)
                    } else {
                        if (CommonUtil.isMicConfigOn()) {
                            // 本来没权限，但配置开着，这时候直接重新加入频道就行
                            VoiceManager.getInstance().rejoinChannel()
                            val result = Js2JavaCommon.GetAgoraStatus()
                            invokeCallback(function, result)
                            fileLog("TouchMic with grant permission, cfg no change {}", result)
                        } else {
                            // 没权限，配置也关着，调整配置后，重新加入频道
                            val result = Js2JavaCommon.TouchMic()
                            VoiceManager.getInstance().rejoinChannel()
                            invokeCallback(function, result)
                            fileLog("TouchMic with grant permission, cfg change {}", result)
                        }
                    }
                }

                override fun noPermission(denied: MutableList<String>?, quick: Boolean) {
                    showCocosPermissionDenyTip(activity, quick)
                    invokeCallback(function, CocosDataUtil.formError(500, ""))
                    fileLog("TouchMic failed no permission: {}", quick)
                }
            })
        }
        wpWebView.registerHandler("TouchSpeaker") { data: String, function: CallBackFunction ->
            val result = if (jsbConfig.shouldExeTouchSpeakerDummy) {
                genTouchSpeakerDummyResult()
            } else {
                Js2JavaCommon.TouchSpeaker()
            }
            invokeCallback(function, result)
            fileLog("TouchSpeaker success: {}", result)
        }
        wpWebView.registerHandler("ShowGameRule") { data: String, function: CallBackFunction ->
            Js2JavaCommon.ShowGameRule()
            invokeCallback(function, okSimpleRsp())
            fileLog("ShowGameRule success")
        }
        wpWebView.registerHandler("ShowGameRule2") { data: String, function: CallBackFunction ->
            Js2JavaCommon.ShowGameRule2(data)
            invokeCallback(function, okSimpleRsp())
        }
        wpWebView.registerHandler("ShowRegulateVolumeDialog") { data: String, function: CallBackFunction ->
            EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_SHOW_REGULATE_VOLUME_DIALOG)
            invokeCallback(function, okSimpleRsp())
            fileLog("ShowRegulateVolumeDialog success")
        }
        wpWebView.registerHandler("GameOver") { data: String, function: CallBackFunction ->
            EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_GAME_OVER)
            invokeCallback(function, okSimpleRsp())
            fileLog("GameOver success")
        }
        wpWebView.registerHandler("IsDebug") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("is_debug", LibBaseUtil.buildDebug())
            } catch (ignored: JSONException) {
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("IsDebug success: {}", result)
        }
        wpWebView.registerHandler("IsDebugUrl") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("is_debug", LibBaseUtil.envDebug())
            } catch (ignored: JSONException) {
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("IsDebugUrl success: {}", result)
        }
        wpWebView.registerHandler("GetAgoraStatus") { data: String, function: CallBackFunction ->
            val result = Js2JavaCommon.GetAgoraStatus()
            invokeCallback(function, result)
            fileLog("GetAgoraStatus success: {}", result)
        }
        wpWebView.registerHandler("MuteRemoteAudioStream") { data: String, function: CallBackFunction ->
            var uid = 0
            var isMute = false
            try {
                val json = JSONObject(data)
                val rsp = JSONObject(data)
                if (json.has("uid")) uid = json.getInt("uid")
                if (json.has("is_mute")) isMute = json.getBoolean("is_mute")
                rsp.put("mute", CommonUtil.muteRemoteAudioStream(uid, isMute))
                val result = formJsonDataNoEncode(200, "", rsp)
                invokeCallback(function, result)
                fileLog("MuteRemoteAudioStream success: {}", result)
            } catch (e: JSONException) {
                invokeCallback(function, CocosDataUtil.formError(500, ""))
                fileLog("MuteRemoteAudioStream failed: {}", e)
            }
        }
        wpWebView.registerHandler("BeforeStartData") { _: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("before_start_data", LittleGame.gameInfo.beforeStartData)
            } catch (e: JSONException) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("BeforeStartData success: {}", result)
        }
        wpWebView.registerHandler("PlayGameEffect") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.play(playInfo)
                invokeCallback(function, okSimpleRsp())
                fileLog("PlayGameEffect success, info={}", playInfo)
            } catch (e: JSONException) {
                invokeCallback(function, CocosDataUtil.formError(500, ""))
                fileLog("PlayGameEffect failed: {}", e)
            }
        }
        wpWebView.registerHandler("PlayGameEffectV2") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.play(playInfo)
                invokeCallback(function, okSimpleRsp())
                fileLog("PlayGameEffectV2 success")
            } catch (e: JSONException) {
                invokeCallback(function, CocosDataUtil.formError(500, ""))
                fileLog("PlayGameEffectV2 failed: {}", e)
            }
        }
        wpWebView.registerHandler("playMusic") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.play(playInfo)
                invokeCallback(function, okSimpleRsp())
                fileLog("playMusic success")
            } catch (e: JSONException) {
                invokeCallback(function, CocosDataUtil.formError(500, ""))
                fileLog("playMusic failed: {}", e)
            }
        }
        wpWebView.registerHandler("GetAppInfo") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("version", LibBaseUtil.getBaseConfig().versionName)
                jsonObject.put("is_ios", false)
                jsonObject.put("app_identifier", LibBaseUtil.getApplication().packageName)
                jsonObject.put("lang", LibBaseUtil.getLang().value)
                jsonObject.put(
                    "prefer_lang",
                    PrefUtil.getInstance().getString(PrefUtil.PREFER_LANG, "")
                )
                val result = formJsonDataNoEncode(200, "", jsonObject)
                invokeCallback(function, result)
                fileLog("GetAppInfo success: {}", result)
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("GetAppInfo failed: {}", e)
            }
        }
        wpWebView.registerHandler("GetItemsConfig") { data: String, function: CallBackFunction ->
            val rsp = JSONObject()
            try {
                val req = JSONObject(data)
                val seq = req.optInt("seq")
                val reqArray = req.getJSONArray("item_ids")
                val rspArray = JSONArray()
                for (i in 0 until reqArray.length()) {
                    val id = reqArray.getInt(i)
                    val propItem = ConfigHelper.getInstance().propConfig.getPropItem(id)
                    rspArray.put(JSONObject(JsonUtil.toJsonString(propItem)))
                }
                rsp.put("items", rspArray)
                val result = formJsonDataNoEncode(200, "", seq, rsp)
                invokeCallback(function, result)
                fileLog("GetItemsConfig success: {}", result)
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("GetItemsConfig failed: {}", e)
            }
        }
        wpWebView.registerHandler("SetGameEffectVolume") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.setVolumeForPath(playInfo.path, playInfo.volume)
                invokeCallback(function, okSimpleRsp())
                fileLog("SetGameEffectVolume success")
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("SetGameEffectVolume failed: {}", e)
            }
        }
        wpWebView.registerHandler("StopGameEffect") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.stop(playInfo.path)
                invokeCallback(function, okSimpleRsp())
                fileLog("StopGameEffect success {}", playInfo.path)
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("StopGameEffect failed: {}", e)
            }
        }
        wpWebView.registerHandler("PlayMusicV2") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.play(playInfo)
                invokeCallback(function, okSimpleRsp())
                fileLog("PlayMusicV2 success")
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("PlayMusicV2 failed: {}", e)
            }
        }
        wpWebView.registerHandler("SetBgMusicVolume") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.setVolumeForPath(playInfo.path, playInfo.volume)
                invokeCallback(function, okSimpleRsp())
                fileLog("SetBgMusicVolume success")
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("SetBgMusicVolume failed: {}", e)
            }
        }
        wpWebView.registerHandler("StopBgMusic") { data: String, function: CallBackFunction ->
            try {
                val playInfo = parseAudioPlayInfo(wpWebView, data)
                CocosSoundUtil.stop(playInfo.path)
                invokeCallback(function, okSimpleRsp())
                fileLog("StopBgMusic success")
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("StopBgMusic failed: {}", e)
            }
        }
        wpWebView.registerHandler("Shake") { data: String, function: CallBackFunction ->
            EventDispatcher.postIceCommandShake(20)
            invokeCallback(function, okSimpleRsp())
            fileLog("Shake success")
        }
        wpWebView.registerHandler("Shake2") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JSONObject(data)
                val time = jsonObject.optInt("time", 20)
                EventDispatcher.postIceCommandShake(time)
                invokeCallback(function, okSimpleRsp())
                fileLog("Shake2 success")
            } catch (e: JSONException) {
                invokeCallback(function, errRsp(e.message))
                fileLog("Shake2 failed: {}", e)
            }
        }
        wpWebView.registerHandler("Share") { data: String, function: CallBackFunction ->
            var type = 0
            var title = ""
            var desc = ""
            var link = ""
            var icon_url = ""
            try {
                val jsonObject = JSONObject(data)
                if (jsonObject.has("type")) {
                    type = jsonObject.getInt("type")
                }
                if (jsonObject.has("title")) {
                    title = jsonObject.getString("title")
                }
                if (jsonObject.has("desc")) {
                    desc = jsonObject.getString("desc")
                }
                if (jsonObject.has("link")) {
                    link = jsonObject.getString("link")
                }
                if (jsonObject.has("icon_url")) {
                    icon_url = jsonObject.getString("icon_url")
                }
            } catch (e: JSONException) {
                logJsonErr(e)
            }
            EventDispatcher.doCocosShare(type, title, desc, link, icon_url)
            invokeCallback(function, okSimpleRsp())
            fileLog("Share success")
        }
        wpWebView.registerHandler("GetStatusBarHeight") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                val height = ScreenUtil.getStatusBarHeight()
                jsonObject.put("height", height)
            } catch (e: JSONException) {
                FLog.e(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetStatusBarHeight success: {}", result)
        }
        wpWebView.registerHandler("GetSid") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("sid", LoginHelper.getSid())
            } catch (e: JSONException) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetSid success: {}", result)
        }
        wpWebView.registerHandler("GetExtraData") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                val paramJson = JSONObject(data)
                val gameType = paramJson.optInt("game_type")
                val key = PrefUtil.COCOS_IS_NEW_USER + gameType
                val isNewUser = PrefUtil.getInstance().getBoolean(key, true)
                jsonObject.put("isUserGuide", isNewUser)
                jsonObject.put("is_voice_room_game", wpWebView.scene == SCENE_VOICE_ROOM)
                PrefUtil.getInstance().setBoolean(key, false)

                var targetUid = LittleGame.gameInfo.followUid
                if (targetUid == 0) {
                    targetUid = LoginHelper.getLoginUid()
                }
                val res = GameChessboardRes.getChessBoardRes(targetUid)
                if (res != null) {
                    jsonObject.put("board_id", res.id)
                    jsonObject.put("board_resource", JSONObject(res.resource))
                    jsonObject.put("board_path", res.boardPath)
                    jsonObject.put("has_cache", res.hasCache)
                }
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetExtraData success: {}", result)
        }
        wpWebView.registerHandler("GetChessboardInfo") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                var targetUid = LittleGame.gameInfo.followUid
                if (targetUid == 0) {
                    targetUid = LoginHelper.getLoginUid()
                }
                val res = GameChessboardRes.getChessBoardRes(targetUid)
                if (res != null) {
                    jsonObject.put("board_id", res.id)
                    jsonObject.put("board_resource", JSONObject(res.resource))
                    jsonObject.put("board_path", res.boardPath)
                    jsonObject.put("has_cache", res.hasCache)
                }
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetChessboardInfo success: {}", result)
        }
        wpWebView.registerHandler("ShowBankruptcyProtectionAlert") { data: String, function: CallBackFunction ->
            try {
                LittleGameUtil.showChipProtectionDialog(JSONObject(data))
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", JSONObject())
            invokeCallback(function, result)
            fileLog("ShowBankruptcyProtectionAlert success: {}", result)
        }
        wpWebView.registerHandler("ShowChipBuyAlert") { data: String, function: CallBackFunction ->
            try {
                LittleGameUtil.showChipExchangeDialog(JSONObject(data))
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", JSONObject())
            invokeCallback(function, result)
            fileLog("ShowChipBuyAlert success: {}", result)
        }
        wpWebView.registerHandler("ShowRecommendSceneAlert") { data: String, function: CallBackFunction ->
            try {
                val jsonObject = JSONObject(data)
                LittleGameUtil.showChipLimitDialog(jsonObject) {
                    wpWebView.callHandler("GameExit", "{}") { }
                }
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", JSONObject())
            invokeCallback(function, result)
            fileLog("ShowRecommendSceneAlert success: {}", result)
        }
        wpWebView.registerHandler("RestartGame") { data: String, function: CallBackFunction ->
            Js2JavaCommon.closeGame()
            try {
                val jsonObject = JSONObject(data)
                val mode = jsonObject.optInt("mode")
                val betLevel = jsonObject.optInt("bet_level")
                EventBus.getDefault().post(CocosRestartMatchEvent(mode, betLevel))
                invokeCallback(function, okSimpleRsp())
                fileLog("RestartGame success")
            } catch (e: Exception) {
                invokeCallback(function, CocosDataUtil.formError(500, e.message))
                fileLog("RestartGame failed: {}", e)
            }
        }
        wpWebView.registerHandler("GetVoiceRoomSafeArea") { data: String, function: CallBackFunction ->
            val jsonObject = JSONObject()
            try {
                jsonObject.put("top", wpWebView.safeTop)
                jsonObject.put("bottom", wpWebView.safeBottom)
            } catch (e: Exception) {
                logJsonErr(e)
            }
            val result = formJsonDataNoEncode(200, "", jsonObject)
            invokeCallback(function, result)
            fileLog("GetVoiceRoomSafeArea success: {}", result)
        }
        wpWebView.registerHandler("OneMoreMatch") { data: String, function: CallBackFunction ->
            EventDispatcher.oneMoreMatch()
            invokeCallback(function, okSimpleRsp())
            fileLog("OneMoreMatch success")
        }
        wpWebView.registerHandler("GetCurrencyCode") { data: String, function: CallBackFunction ->
            val currency = IapApi::class.impl().currency
            val result = formJsonDataNoEncode(200, "success", JSONObject().apply {
                put("currency", currency)
            })
            invokeCallback(function, result)
            fileLog("GetCurrencyCode success: {}", result)
        }
        wpWebView.registerHandler("SetGameInfo") { data: String, function: CallBackFunction ->
            val jsonObject = JsonUtil.toObject(data)
            val gameType = jsonObject.getInt("game_type", -1)
            val gameMode = jsonObject.getInt("game_mode", -1)
            val betLevel = jsonObject.getInt("bet_level", -1)
            val mode = jsonObject.getInt("mode", -1)
            val currencyType = jsonObject.getInt("currency_type", GameConfig.CURRENCY_CHIP)
            val rid = jsonObject.getInt("rid", -1)
            val uids: List<Int> = jsonObject.getArray("uids").toList(Int::class.java)
            val cid = jsonObject.getInt("competition_id", -1)
            if (gameType != -1 && gameMode != -1 && betLevel != -1 && mode != -1) {
                LittleGame.updateCocosGameInfo(
                    gameType, gameMode, betLevel, mode, currencyType,
                    rid, uids, cid
                )
            }
            val result = formJsonDataNoEncode(200, "success", JSONObject())
            invokeCallback(function, result)
            fileLog("SetGameInfo success: {}", result)
        }
        registerHandlers(wpWebView)
    }

    /**
     * 新增接口的时候，需要检查文档更新。
     * https://wepie.feishu.cn/wiki/Q5wZw9l5ViWBFMkuFpuc2uLlnVe
     */
    private fun registerHandlers(wpWebView: CocosWebView) {
        listOf(
            GetAssetBundleInfo(),
            ReloadAssetBundle(wpWebView.toLife()),
            LoadABBundle(wpWebView),
            GetMiniGameStartTime(wpWebView),
            CocosApiGetLoadData(wpWebView),
            CocosApiRelaunch(wpWebView),
            CocosRequestAudioPermission(),
            CocosJoinChannel(wpWebView),
            CocosExitChannel(wpWebView),
            CocosToggleMic(wpWebView),
            CocosToggleSound(),
            CocosMuteStream(),
            CocosApiOpenDeepLink(),
            CocosApiInviteDialog(),
            CocosApiShowUserDialog(),
            CocosApiClearDialog(wpWebView),
            CocosApiShowPlatGiftDialog(wpWebView),
            CocosApiShowInputView(),
            CocosApiShowInputView2(),
            CocosApiShowInputView3(wpWebView),
            CocosApiGameStateChanged(wpWebView),
            CocosApiShowNotEnoughAlert(), // 货币不足
            CocosInnerPurchase(),
            CocosApiWriteLog(),
        ).forEach {
            wpWebView.registerHandler(it.apiFuncName, it.bridge)
        }
    }


    @JvmStatic
    fun clickShare(data: String) {
        try {
            val json = JSONObject(data)
            var ids = ""
            if (json.has("topic_ids")) ids = json.getString("topic_ids")
            fileLog("clickShare {}", ids)
            EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_SHARE_GAME, 0, 0, ids)
        } catch (e: JSONException) {
            logJsonErr(e)
        }
    }

    private fun genTouchSpeakerDummyResult(): String {
        val argMap = Js2JavaCommon.genAgoraStatusArgMap(false, false)
        val resultObj = BaseCocosApi.CocosApiResult.ok(argMap)
        return JsonUtil.toJsonString(resultObj)
    }

    @JvmStatic
    fun callJsGameExit(webView: CocosWebView) {
        webView.callHandler("GameExit", "{}") { }
    }

    private fun invokeCallback(callBackFunction: CallBackFunction, jsonText: String?) {
        callBackFunction.onCallBack(jsonText)
    }

    private fun parseAnimRectInfo(jsonObject: JsonObject): AnimaRect? {
        try {
            val x = jsonObject.getInt("x")
            val y = jsonObject.getInt("y")
            val w = jsonObject.getInt("w")
            val h = jsonObject.getInt("h")
            return AnimaRect(x, y, w, h)
        } catch (e: Exception) {
            //解析失败返回null，走默认逻辑，显示全屏数据
            return null
        }
    }

    /**
     * 从 js bridge 传入的 data 中解析出音频播放信息。
     * 默认值 loop = false
     * 默认值 volume = 记忆的值
     * 默认值 effectName = "" 未传递默认值时会播放失败，当前业务不做额外的处理，cocos 自己保证传入值正常
     * @return 解析的播放信息
     */
    private fun parseAudioPlayInfo(wpWebView: CocosWebView, data: String): AudioPlayInfo {
        val jsonObject = JsonUtil.toObject(data)
        val effectName = jsonObject.getString("effect_name")
        val bundlePath = CocosAssetBundleResLoader.mapUriToFilePath(effectName)
        val fullPath = bundlePath ?: (wpWebView.path + effectName)
        val isLoop = jsonObject.getBoolean("is_loop", false)
        val defaultVolume = VolumeUtil.getEffectOrBGMMediaVolume(isLoop) / 100f
        val volume = jsonObject.getFloat("volume", defaultVolume)
        return AudioPlayInfo(fullPath, isLoop, volume)
    }

    @JvmStatic
    fun release() {
    }


    private fun jumpDeeplink(deeplink: String, resume: Boolean) {
        val activity = ActivityTaskManager.getInstance().topActivity
        if (activity != null) {
            if (resume) {
                ToastUtil.debugShow("发现" + activity + "重建了,正在跳转")
            }
            EventBus.getDefault().post(CocosMainActivityFinishEvent())
            JumpApi::class.impl().jumpToDeepLink(activity, deeplink, "大西瓜导流")
        } else {
            ThreadUtil.runOnUiThreadDelay(500) {
                ToastUtil.debugShow("主进程Activity被销毁了,等待重建.")
                jumpDeeplink(deeplink, true)
            }
        }
    }

    private fun logJsonErr(e: Exception) {
        HLog.e(TAG, "json error: {}", e)
        FLog.e(e)
    }

    private fun fileLog(msg: String, vararg args: Any?) {
        HLog.d(TAG, HLog.USR, msg, *args)
    }

    /**
     * 辅助音频播放的结构
     */
    class AudioPlayInfo(
        /**
         * 完整路径
         */
        val path: String,
        /**
         * 是否循环播放，一般来说，循环播放代表背景音乐
         */
        val loop: Boolean,
        /**
         * 音量， 0.0-1.0
         */
        val volume: Float
    ) {
        override fun toString(): String {
            return "AudioPlayInfo(path='$path', loop=$loop, volume=$volume)"
        }
    }
}