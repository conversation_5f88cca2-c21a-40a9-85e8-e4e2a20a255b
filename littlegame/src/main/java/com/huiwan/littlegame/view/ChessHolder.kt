package com.huiwan.littlegame.view

import android.view.View
import android.view.View.VISIBLE
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.CollectionGradeConfigItem
import com.huiwan.configservice.constentity.JKHomeConfig
import com.huiwan.configservice.constentity.propextra.JackarooSkinExtra
import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.huiwan.decorate.ChessSkinShowView
import com.huiwan.littlegame.R
import com.huiwan.user.entity.User
import com.huiwan.widget.SimpleOutlineProvider
import com.wepie.libimageloader.WpImageLoader


class ChessHolder(root: View) {

    private val chessLay: View = root.findViewById(R.id.main_chess_view)
    private val collectionTv: TextView = root.findViewById(R.id.chess_collection_tv)
    private val chessBg: ImageView = root.findViewById(R.id.chess_bg)
    private val chessIv: ChessSkinShowView = root.findViewById(R.id.chess_iv)

    init {
        chessBg.clipToOutline = true
        chessBg.outlineProvider = SimpleOutlineProvider(ScreenUtil.dip2px(12F).toFloat())
    }

    fun updateChessUI(user: User) {
        chessLay.visibility = VISIBLE
        val inUseChessPropId = user.collectionInfo.getInUseChessPropId()
        collectionTv.text = user.collectionInfo.collectionValue.toString()
        val propItem = ConfigHelper.getInstance().propConfig
            .getPropItem(inUseChessPropId)
        JKHomeConfig::class.instance().getCollectionGrade(inUseChessPropId).gradeBackgroundUrl
        chessIv.bind(propItem, false)
        getChessGradeConfig(inUseChessPropId)?.let {
            WpImageLoader.load<String>(it.gradeBackgroundUrl, chessBg)
        }
    }

    private fun getChessGradeConfig(inUseChessPropId: Int): CollectionGradeConfigItem? {
        val grade = PropItemConfig::class.java.instance().getPropItem(inUseChessPropId)
            ?.getExtraByType(JackarooSkinExtra::class.java)?.itemGrade ?: 0
        if (grade < 0) {
            return null
        } else {
            return JKHomeConfig::class.java.instance().collectionGradeConfig[grade]
        }
    }
}