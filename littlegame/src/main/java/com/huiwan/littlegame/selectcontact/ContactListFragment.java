package com.huiwan.littlegame.selectcontact;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.huiwan.base.util.StringUtil;
import com.huiwan.component.activity.BaseFragment;
import com.huiwan.constants.BaseConstants;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.ChatApi;
import com.huiwan.lib.api.plugins.IFunctionShieldApi;
import com.huiwan.littlegame.R;
import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.pinyin.PinyinComparator;
import com.huiwan.widget.RecyclerViewSideBar;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 联系人列表页面，分两种场景：
 * 最近联系人：和分享页面一样，不显示字母分隔符和右侧的字母滚动条
 * 所有联系人：和Friends界面一样，显示分隔符和右侧滚动条
 * 没必要写两个fragment，复用一个调下界面就行
 */
public class ContactListFragment extends BaseFragment {

    public static final String TAG = ContactListFragment.class.getName();
    private final boolean isRecentContactScene;
    private final OnContactSelectListener contactSelectListener;
    private RecyclerView contactListRv;
    private RecyclerViewSideBar rvSideBar;
    private HWUIEmptyView emptyView;
    private ContactListAdapter contactListAdapter;
    private final List<FriendInfo> contactList = new ArrayList<>();

    /**
     * @param isRecentContactScene 最近联系人场景下不显示字母分隔符和右侧字幕滚动条
     * @param listener 选中联系人后的回调
     */
    public ContactListFragment(boolean isRecentContactScene, @NonNull OnContactSelectListener listener) {
        this.isRecentContactScene = isRecentContactScene;
        this.contactSelectListener = listener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_contact_list, container);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        init(view);
    }

    private void init(View view) {
        rvSideBar = view.findViewById(R.id.contact_list_bar);
        if (isRecentContactScene) {
            rvSideBar.setVisibility(View.GONE);
        }
        emptyView = view.findViewById(R.id.empty_view);
        // 非最近联系人场景的文案是对的，不用改
        if (isRecentContactScene) {
            emptyView.setText(ResUtil.getStr(com.wepie.lib.share.R.string.room_invite_dialog_recent_none));
        }
        contactListRv = view.findViewById(R.id.contact_list);
        contactListRv.setLayoutManager(new LinearLayoutManager(getContext()));
        contactListAdapter = new ContactListAdapter(getContext(), contactSelectListener);
        contactListAdapter.setHideAbbreviationDivider(isRecentContactScene);
        contactListRv.setAdapter(contactListAdapter);
        refreshContactList();
    }

    private List<FriendInfo> getAllContactList() {
        List<FriendInfo> friendList = FriendInfoCacheManager.getInstance().getFriendList();
        int selfUid = LoginHelper.getLoginUid();
        // 用removeIf或stream.filter.toList，部分机型闪退
        for (int i = friendList.size() - 1; i >= 0 ; i--) {
            int uid = friendList.get(i).getUid();
            if (uid == BaseConstants.JUDGE_UID || uid == selfUid) {
                friendList.remove(i);
            }
        }
        return friendList;
    }

    private List<FriendInfo> getRecentContactList() {
        List<Integer> recentContactUidList = ApiService.of(ChatApi.class).getRecentContactList();
        List<FriendInfo> recentChatFriends = new ArrayList<>(recentContactUidList.size());
        int selfUid = LoginHelper.getLoginUid();
        for (Integer uid : recentContactUidList) {
            FriendInfo friendInfo = FriendInfoCacheManager.getInstance().getFriendInfoByUid(uid);
            if (friendInfo != null && !UserService.get().isSystemUser(uid) && uid != selfUid) {
                recentChatFriends.add(friendInfo);
            }
        }
        return recentChatFriends;
    }

    /**
     * 下面两个函数从FriendsListFragment{@link com.wepie.wespy.module.contact.friendlist.FriendsListFragment}拷过来的稍微改了下
     */
    private void refreshContactList() {
        List<FriendInfo> list = isRecentContactScene ? getRecentContactList() : getAllContactList();
        Collections.sort(list, new PinyinComparator());
        contactList.addAll(list);
        contactListAdapter.refresh(contactList);
        char[] nameChars = new char[0];
        if (!ApiService.of(IFunctionShieldApi.class).hideSideBar()) {
            nameChars = getFirstChars(list);
        }
        if (nameChars.length > 0) {
            rvSideBar.setRecycleView(contactListRv, nameChars);
        } else {
            rvSideBar.setVisibility(View.GONE);
        }

        // recyclerView不用管了，反正没数据的话本身也显示是空白
        emptyView.setVisibility(list.isEmpty() ? View.VISIBLE : View.GONE);
    }

    private static char[] getFirstChars(List<FriendInfo> list) {
        ArrayList<String> nameCharList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            String name = list.get(i).getNameFirstLetter();
            String firstChar = name.substring(0, 1).toUpperCase();
            if (!StringUtil.isCharacter(firstChar)) {
                firstChar = "#";
            }
            if (!nameCharList.contains(firstChar)) {
                nameCharList.add(firstChar);
            }
        }
        int len = nameCharList.size();
        char[] chars = new char[len];
        for (int i = 0; i < len; i++) {
            chars[i] = nameCharList.get(i).charAt(0);
        }
        return chars;
    }
}
