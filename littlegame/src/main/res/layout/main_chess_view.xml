<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/chess_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:translationY="17dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/chess_bg"
            android:layout_width="match_parent"
            android:layout_height="115dp"
            android:adjustViewBounds="true"
            android:src="@drawable/default_chess_bg"
            android:clipToOutline="true"
            android:scaleType="centerCrop"
            android:scaleX="@integer/image_scale_x" />

    </LinearLayout>



    <com.huiwan.decorate.ChessSkinShowView
        android:id="@+id/chess_iv"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:clipChildren="false"
        android:translationY="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/chess_container"
        app:layout_constraintEnd_toEndOf="@+id/chess_container" />

    <LinearLayout
        android:id="@+id/chess_collection_lay"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/shape_00000080_corner16"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="6dp"
        app:layout_constraintBottom_toBottomOf="@+id/chess_container"
        app:layout_constraintStart_toStartOf="@+id/chess_container">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/collection_chip" />

        <TextView
            android:id="@+id/chess_collection_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:textColor="@color/color_white"
            android:textSize="10dp"
            tools:text="1212" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>