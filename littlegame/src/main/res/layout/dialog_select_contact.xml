<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="Title"
        android:textColor="@color/color_text_accent_dark"
        android:textSize="16sp"
        android:textStyle="bold" />

    <com.huiwan.widget.SearchBar
        android:id="@+id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:hint="@string/friend_list_search_user_hint" />

    <RelativeLayout
        android:id="@+id/tab_layout_bg"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginTop="10dp"
        android:layout_marginHorizontal="16dp"
        android:padding="1dp"
        android:background="@drawable/shape_fafafa_corner23">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textStyle="bold"
            android:background="@drawable/shape_fafafa_corner23"
            app:tabRippleColor="@null"
            app:tabTextAppearance="@android:style/TextAppearance.Widget.TabWidget"
            app:tabIndicator="@drawable/shape_fafafa_corner23"
            app:tabIndicatorColor="@color/white"
            app:tabIndicatorFullWidth="true"
            app:tabIndicatorHeight="34dp"
            app:tabMinWidth="100dp"
            app:tabMode="fixed"
            app:tabSelectedTextColor="@color/black"
            app:tabTextColor="#999cb4" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/list_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:visibility="gone"
            android:id="@+id/search_result_list_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.huiwan.base.ui.empty.HWUIEmptyView
            android:visibility="gone"
            android:id="@+id/empty_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:icon_type="base_empty_find_empty"
            app:text="@string/search_empty_result"
            app:text_color="@color/color_text_tertiary" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>