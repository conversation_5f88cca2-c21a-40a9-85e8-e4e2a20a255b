apply from: "../base_module.gradle"

android {
    defaultConfig {
        buildConfigField "Boolean", "USE_PAD", "${rootProject.ext.USE_PAD}"
    }
    android.buildFeatures.buildConfig true
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    //这里引用的模块注意多进程处理，初始化等等
    implementation libs.eventBus
    implementation libs.okhttp
    implementation project(path: ':lib:baseutil')
//    implementation project(path: ':lib:cocos')
    implementation project(path: ':lib:floating')
    implementation project(path: ':lib:libwidget')
    implementation project(path: ':lib:anim')
    implementation project(path: ':lib:liblog')
    implementation project(path: ":lib:api-plugin:track")
    implementation project(path: ':service:UserService')
    implementation project(path: ':service:VoiceService')
    implementation project(path: ':service:ConfigService')
    implementation project(path: ':lib:libtcp')
    implementation project(path: ':lib:libdialog')
    implementation project(path: ':lib:Emojilib')
    implementation project(path: ':lib:libproto')
    implementation project(path: ':lib:hwconstants')
    implementation project(path: ':lib:store')
    implementation project(path: ':lib:os-libshare')
    implementation project(path: ':component:decorate')
    implementation project(path: ':component:gift')
    implementation project(path: ':component:barrage')
    implementation project(path: ':component:activity')
    implementation project(path: ':lib:libhttp')
    implementation project(path: ':lib:libimageloader')
    implementation project(path: ':lib:api')
    implementation project(path: ':lib:libdownload')
    implementation project(path: ':lib:api-plugin:add-friend')
    implementation project(path: ':lib:api-plugin:voice-api')
    implementation project(path: ':lib:ipc')
    implementation project(path: ':lib:api-plugin:share')
    implementation(libs.viewpager2) {
        transitive false
    }
    implementation project(":lib:libpermission")
    implementation project(path: ':lib:api-plugin:avatar')
    implementation project(path: ':component:adjustvolume')
    implementation project(path: ':module:game-match')
    implementation project(path: ':module:base-littlegame')
    implementation project(path: ':component:game-chip')
    api project(path: ':lib:JsBridge')
    implementation project(path: ':lib:libwebview')
    implementation libs.androidx.webkit
    if (rootProject.ext.USE_PAD.toBoolean()) {
        implementation project(":lib:asset-delivery")
    }
}