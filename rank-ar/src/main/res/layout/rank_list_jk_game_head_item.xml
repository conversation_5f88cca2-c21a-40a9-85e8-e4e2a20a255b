<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="216dp"
    android:clipChildren="false">

    <ImageView
        android:id="@+id/rank_flag_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="matrix"
        android:scaleX="@integer/image_scale_x"
        android:src="@drawable/rank_flag_top1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rank_chess_lay"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:clipChildren="false"
        app:layout_constraintEnd_toEndOf="@id/rank_flag_rank"
        app:layout_constraintStart_toStartOf="@id/rank_flag_rank"
        app:layout_constraintTop_toTopOf="@id/rank_flag_rank">

        <com.huiwan.decorate.ChessSkinShowView
            android:id="@+id/rank_chess_iv"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:clipChildren="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/rank_chess_gradient"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/rank_flag_crown_bg"
        android:layout_width="0dp"
        android:layout_height="29dp"
        android:scaleType="fitXY"
        android:src="@drawable/rank_crown_rank1_bg"
        android:scaleX="@integer/image_scale_x"
        app:layout_constraintEnd_toEndOf="@+id/rank_flag_rank"
        app:layout_constraintStart_toStartOf="@+id/rank_flag_rank"
        app:layout_constraintTop_toTopOf="@+id/rank_flag_rank" />

    <ImageView
        android:id="@+id/rank_flag_crown_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="1dp"
        android:src="@drawable/rank_crown_rank1"
        app:layout_constraintStart_toStartOf="@+id/rank_flag_rank"
        app:layout_constraintTop_toTopOf="@+id/rank_flag_rank" />


    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@+id/rank_flag_rank"
        app:layout_constraintTop_toTopOf="@+id/rank_flag_rank">

        <View
            android:id="@+id/collection_icon_view"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:background="@drawable/collection_chip"
            android:visibility="gone" />

        <View
            android:id="@+id/chip_icon_view"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:background="@drawable/game_chip"
            android:visibility="gone" />

        <TextView
            android:id="@+id/value_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="1dp"
            android:textColor="@color/white"
            android:textFontWeight="700"
            android:textSize="12dp"
            android:textStyle="bold"
            tools:text="1234567" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/rank_head_rank"
        android:layout_width="42dp"
        android:layout_height="42dp"
        app:border_end_color="#FFC28C59"
        app:border_star_color="#FFFAF0D4"
        app:gradiant_area_size="48dp"
        app:head_border_width="1dp"
        app:layout_constraintBottom_toTopOf="@+id/rank_name_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:use_gradiant_border="true"
        tools:src="@drawable/default_head_icon" />

    <com.huiwan.decorate.NameTextView
        android:id="@+id/rank_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        android:maxWidth="57dp"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14dp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/level_lay"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:ntv_scene="rank"
        tools:text="1234567" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/level_lay"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginBottom="24dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/rank_flag_rank"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.huiwan.decorate.JackarooLevelView
            android:id="@+id/jk_level_view"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            tools:background="@drawable/circle_red" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/grade_king_lay"
                android:layout_width="wrap_content"
                android:layout_height="12dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="-7dp"
                android:layout_toEndOf="@id/grade_diamond_iv"
                android:background="@drawable/rank_grade_star_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="8dp"
                android:paddingEnd="4dp"
                android:visibility="gone">

                <View
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:background="@drawable/grade_star" />

                <TextView
                    android:id="@+id/grade_king_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="12dp"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:textColor="#FFED53"
                    android:textSize="10dp"
                    android:textStyle="bold" />
            </LinearLayout>

            <ImageView
                android:id="@+id/grade_diamond_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentStart="true"
                android:layout_marginStart="2dp" />
        </RelativeLayout>
    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>