package com.wepie.wespy.module.pay.commonapi;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.*;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.IapApi;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.FLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.helper.shence.ShenceUtil;
import com.wepie.wespy.model.entity.other.WejoyDiscountInfo;
import com.wepie.wespy.net.http.api.CouponApi;
import com.wepie.wespy.net.http.api.OrderApi;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.*;

/**
 * date 2020/6/30
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class GoodsListRvHelper {
    private final RecyclerView rv;
    private final Adapter adapter = new Adapter();
    private UpdateCallback updateCallback;
    private final ProgressDialogUtil dialogUtil = new ProgressDialogUtil();
    private final Map<String, Object> extTrackData = new HashMap<>();

    private final Runnable updateRunner = new Runnable() {
        @Override
        public void run() {
            if (updateCallback != null) {
                updateCallback.onNeedUpdate();
            }
        }
    };

    public GoodsListRvHelper(RecyclerView rv, int spanCount) {
        this.rv = rv;
        rv.setLayoutManager(new GridLayoutManager(rv.getContext(), spanCount));
        rv.setAdapter(adapter);
        if (!ScreenUtil.isLandScape(rv.getContext())) {
            rv.addItemDecoration(new GoodsItemDecoration());
        }
    }

    public void addTrackData(Map<String, Object> extTrackData) {
        if (extTrackData != null) {
            this.extTrackData.putAll(extTrackData);
        }
    }

    public void setUpdateCallback(UpdateCallback updateCallback) {
        this.updateCallback = updateCallback;
    }

    public void refreshGameCoinList(String type) {
        dialogUtil.showLoadingDelay(rv.getContext());

        OrderApi.getGoodsList(type, ApiService.of(IapApi.class).getCurrency(), new LifeDataCallback<List<WespyGoods>>(rv) {
            @Override
            public void onSuccess(Result<List<WespyGoods>> result) {
                dialogUtil.hideLoading();
                adapter.update(result.data, extTrackData, updateCallback, type);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
                dialogUtil.hideLoading();
            }
        });
    }

    public void refresh() {
        dialogUtil.showLoadingDelay(rv.getContext());
        OrderApi.getGoodsList(OrderApi.GoodsType.DIAMOND, ApiService.of(IapApi.class).getCurrency(), new LifeDataCallback<List<WespyGoods>>(rv) {
            @Override
            public void onSuccess(Result<List<WespyGoods>> result) {
                updateDiscount(result.data);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
                dialogUtil.hideLoading();
            }
        });
    }

    private void updateDiscount(List<WespyGoods> goodsList) {
        ILife life = ILifeUtil.toLife(rv);
        CouponApi.getDiscount(new LifeDataCallback<WejoyDiscountInfo>(life) {
            @Override
            public void onSuccess(Result<WejoyDiscountInfo> result) {
                dialogUtil.hideLoading();
                WejoyDiscountInfo.Coin coin = result.data.coin;
                if (TimeUtil.getServerTime() > coin.startTimeInSec * 1000 && TimeUtil.getServerTime() < coin.endTimeInSec * 1000) {
                    ViewExKt.postAutoCancel(rv, coin.endTimeInSec * 1000 - TimeUtil.getServerTime(), updateRunner);
                    for (WespyGoods goods : goodsList) {
                        for (WejoyDiscountInfo.Coin.Info info : coin.infoList) {
                            if (info.goodsId == goods.goods_id) {
                                goods.setInDiscount(true);
                                goods.setDiscountLabel(info.label);
                                goods.setDiscountUrl(info.iconUrl);
                            }
                        }
                    }
                }
                adapter.update(goodsList, extTrackData, updateCallback);
                if (null != updateCallback) {
                    updateCallback.updateDiscountInfo(result.data);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
                adapter.update(goodsList, extTrackData, updateCallback);
                dialogUtil.hideLoading();
            }
        });
    }

    public static class Holder extends RecyclerView.ViewHolder {
        Holder(View view) {
            super(view);
        }
    }

    public static class Adapter extends RecyclerView.Adapter<Holder> {
        private final ArrayList<WespyGoods> list = new ArrayList<>();
        private UpdateCallback updateCallback;
        private Map<String, Object> trackExt;
        private String type = OrderApi.GoodsType.DIAMOND;

        public void update(List<WespyGoods> list, Map<String, Object> trackExt, UpdateCallback updateCallback) {
            this.updateCallback = updateCallback;
            this.trackExt = trackExt;
            this.list.clear();
            this.list.addAll(list);
            notifyDataSetChanged();
        }

        public void update(List<WespyGoods> list, Map<String, Object> trackExt, UpdateCallback updateCallback, String type) {
            this.updateCallback = updateCallback;
            this.trackExt = trackExt;
            this.type = type;
            this.list.clear();
            this.list.addAll(list);
            notifyDataSetChanged();
        }

        @Override
        public void onBindViewHolder(Holder holder, int position) {
            ((GoodsItemView) holder.itemView).onBind(list.get(position), trackExt, position, updateCallback, type);
        }

        @Override
        public Holder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new Holder(new GoodsItemView(parent.getContext()));
        }

        @Override
        public int getItemCount() {
            return list.size();
        }
    }

    /**
     * 当前内部只有一个 ImageView, 没有添加 layout 文件
     */
    static class GoodsItemView extends FrameLayout {
        private final ImageView iconIv;
        private final TextView priceTv;
        private final TextView coinTv;
        private final TextView coinTagTv;

        private final ViewGroup coinLay;
        private final ImageView iconLittleIv;

        private int position;
        private WespyGoods goods;
        private Map<String, Object> trackExt;
        private UpdateCallback updateCallback;
        private String type = "";
        public GoodsItemView(Context context) {
            super(context);
            LayoutInflater.from(context).inflate(R.layout.goods_coin_list_item, this);
            coinLay = findViewById(R.id.coin_lay);
            iconIv = findViewById(R.id.icon_iv);
            priceTv = findViewById(R.id.price_tv);
            coinTv = findViewById(R.id.coin_tv);
            coinTagTv = findViewById(R.id.coin_tag_tv);
            iconLittleIv = findViewById(R.id.icon_little_iv);
            setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    IDAuthCheckManager.doTaskOrShowNeedCertificate(v.getContext(), AuthApi.SCENE_CHARGE, new IDAuthCheckManager.TaskCallback() {
                        @Override
                        public void onDoTask() {
                            if (goods != null) {
                                checkBuy(goods);
                            }
                        }
                    });
                }
            });
        }

        private void setMarquee() {
            this.coinTagTv.post(() -> {
                coinTagTv.setEllipsize(TextUtils.TruncateAt.MARQUEE);
                coinTagTv.setSelected(true);
                coinTagTv.setSingleLine(true);
                coinTagTv.setFocusable(true);
                coinTagTv.setFocusableInTouchMode(true);
            });
        }

        private void checkBuy(WespyGoods goods) {
            notifyUpdate();
            Map<String, Object> map = new HashMap<>();
            String subScreenName;
            if (Objects.equals(type, OrderApi.GoodsType.GOLD)) {
                subScreenName = ResUtil.getStr(R.string.jackaroo_track_coin_sub);
            } else {
                subScreenName = ResUtil.getStr(R.string.jackaroo_track_coin_main);
            }
            map.put("btn_pos", position);
            map.put("sub_screen_name", subScreenName);
            // 已经检查了所有场景，不会拿不到screen_name的
            String screenName = String.valueOf(trackExt.get("screen_name") == null ? TrackScreenName.PAY_PAGE : trackExt.get("screen_name"));
            if (trackExt != null) {
                map.putAll(trackExt);
            }
            ShenceEvent.appClick(screenName, ResUtil.getString(R.string.select_pay_price), map);
            ShenceUtil.propertyList(this, ShenceUtil.List_Property_Gold_Coin, goods.goods_price);
            Activity activity = ContextUtil.getActivityFromContext(getContext());
            if (activity != null) {
                InnerPayApi.showPay(goods, activity, this.updateCallback, map);
            }
        }

        public void onBind(WespyGoods goods, Map<String, Object> trackExt, int index, UpdateCallback updateCallback, String type) {
            this.trackExt = trackExt;
            this.updateCallback = updateCallback;
            this.position = index;
            this.goods = goods;
            this.type = type;
            if (TextUtils.isEmpty(goods.getDiscountLabel())) {
                this.coinTagTv.setVisibility(GONE);
                ViewUtil.setTopMargins(coinLay, ResUtil.getResource().getDimensionPixelSize(R.dimen.coin_item_coin_top_margin) + ScreenUtil.dip2px(4f));
            } else {
                ViewUtil.setTopMargins(coinLay, ResUtil.getResource().getDimensionPixelSize(R.dimen.coin_item_coin_top_margin_discount) + ScreenUtil.dip2px(4f));
                this.coinTagTv.setVisibility(VISIBLE);
                this.coinTagTv.setText(goods.getDiscountLabel());
                setMarquee();
            }
            ViewUtil.setBottomMargins(priceTv, ResUtil.getResource().getDimensionPixelSize(R.dimen.coin_item_price_bottom_margin) - ScreenUtil.dip2px(2f));
            this.priceTv.setText(ResUtil.getResource().getString(R.string.price_currency_s_s, goods.currency, goods.goods_price));
            if (Objects.equals(type, OrderApi.GoodsType.GOLD)) {
                this.iconLittleIv.setBackgroundResource(R.drawable.game_chip);
                this.coinTv.setText(fmtChipCoin(goods.chipCoin));
            } else {
                this.iconLittleIv.setBackgroundResource(R.drawable.wejoy_coin_icon);
                this.coinTv.setText(goods.goods_coin);
            }
            String url = (goods.isInDiscount() && !TextUtils.isEmpty(goods.getDiscountUrl())) ?
                    goods.getDiscountUrl() : goods.getIconUrl();
            WpImageLoader.load(url, iconIv);
            coinTv.requestLayout();
        }

        private void notifyUpdate() {
            if (updateCallback != null) {
                updateCallback.onNeedUpdate();
            }
        }

        private String fmtChipCoin(String chipCoin) {
            try {
                int chipCoinI = Integer.parseInt(chipCoin);
                if (chipCoinI >= 1000_000) {
                    DecimalFormatSymbols dfs = new DecimalFormatSymbols(Locale.ENGLISH);
                    return new DecimalFormat("#.0", dfs).format(chipCoinI / 1000_000f) + "M";
                }
                return StringUtil.formatInteger(chipCoinI, "0.0");
            } catch (Exception e) {
                FLog.e(e);
            }
            return chipCoin;
        }
    }

    public void clear() {
        rv.removeCallbacks(updateRunner);
    }

    public interface UpdateCallback extends InnerPayApi.PayResultCallback {
        void onNeedUpdate();

        default void updateDiscountInfo(WejoyDiscountInfo discountInfo) {

        }
    }
}
