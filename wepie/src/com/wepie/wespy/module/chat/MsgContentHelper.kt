package com.wepie.wespy.module.chat

import android.text.TextUtils
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.store.database.WPModel
import com.huiwan.store.database.WPStore
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.UserSimpleInfoCallback
import com.huiwan.user.entity.UserSimpleInfo
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.ChatMsg
import com.wepie.wespy.model.entity.ConversationInfo
import com.wepie.wespy.model.entity.GroupChatMsg
import com.wepie.wespy.model.entity.InviteCardInfo
import com.wepie.wespy.model.entity.WPMessage
import com.wepie.wespy.model.entity.match.GameInviteInfo
import com.wepie.wespy.module.chat.MsgContentHelper.GroupInfoType.getGroupMsgByMid
import com.wepie.wespy.module.chat.MsgContentHelper.PersonalInfoType.getPersonalMsgByMid
import com.wepie.wespy.module.chat.gamemodel.MsgGroupShareModel
import com.wepie.wespy.module.chat.gamemodel.MsgH5LinkModel
import com.wepie.wespy.module.chat.gamemodel.MsgTextHelper
import com.wepie.wespy.module.game.game.activity.TextSpanUtil
import com.wepie.wespy.utils.ShellPackageUtil
import org.json.JSONException
import org.json.JSONObject

/**
 * https://wepie.yuque.com/we_play/manual/zg6tbv1d80ro8flt?singleDoc#
 * 单聊和群聊以及消息列表content的统一
 * 适用于1.消息列表的content 2.回复消息的引用content
 */
object MsgContentHelper {

    /**
     * 用于消息tab的列表显示
     */
    fun getContent(info: ConversationInfo, onSuccess: (String) -> Unit) {
        ConversationInfoType.getContent(info, onSuccess)
    }

    /**
     * 用于回复的内容显示
     * 不能回复系统消息
     */
    fun getContent(msg: WPMessage?, onSuccess: (String) -> Unit) {
        PersonalInfoType.getContent(msg, onSuccess)
    }

    /**
     * 用于回复的内容显示
     * 不能回复系统消息
     */
    fun getContent(msg: ChatMsg?, onSuccess: (String) -> Unit) {
        GroupInfoType.getContent(msg, onSuccess)
    }

    /**
     * 绑定info和msg，减少异步调用
     */
    fun connectInfoWithMsg(info: ConversationInfo) {
        ConversationInfoType.connectInfoWithMsg(info)
    }

    private sealed class ContentType<T> {
        abstract fun getContent(t: T?, onSuccess: (String) -> Unit)
    }

    private object ConversationInfoType : ContentType<ConversationInfo>() {
        /**
         * 用于消息tab的列表显示
         */
        override fun getContent(info: ConversationInfo?, onSuccess: (String) -> Unit) {
            if (info == null) {
                onSuccess.invoke("")
                return
            }
            if (!TextUtils.isEmpty(info.draft) || !TextUtils.isEmpty(info.ref_mid)) {
                onSuccess.invoke(info.draft)
                return
            }
            if (info.isPersonalChat) {
                getPersonalContent(info, onSuccess)
            } else {
                getGroupContent(info, onSuccess)
            }
        }

        /**
         * 绑定info和msg，减少异步调用
         */
        fun connectInfoWithMsg(info: ConversationInfo) {
            if (info.isPersonalChat) {
                if (info.wpMessage == null) {
                    getPersonalMsgByMid(info.mid) {
                        info.wpMessage = it
                    }
                }
            } else {
                if (info.chatMsg == null) {
                    getGroupMsgByMid(info.mid) {
                        info.chatMsg = it
                    }
                }
            }
        }

        /**
         * 单聊消息的content
         */
        private fun getPersonalContent(info: ConversationInfo, onSuccess: (String) -> Unit) {
            if (!TextUtils.isEmpty(info.draft) || !TextUtils.isEmpty(info.ref_mid)) {
                onSuccess.invoke(info.draft)
                return
            }
            PersonalInfoType.getPersonalContentInner(info, { onMsg ->
                // 无论当前是否有缓存，都立即返回，然后异步设置缓存
                // 消息列表刷新很频繁，异步填数据会有性能问题，故只异步获取，同步刷新
                onMsg.invoke(info.wpMessage)
                if (info.wpMessage == null) {
                    PersonalInfoType.getPersonalMsgByMid(info.mid) {
                        info.wpMessage = it
                    }
                }
            }, onSuccess)
        }

        /**
         * 群聊消息的content
         */
        private fun getGroupContent(info: ConversationInfo, onSuccess: (String) -> Unit) {
            fun getGroupContent(pref: String) {
                GroupInfoType.getGroupContentInner(info, pref, { onMsg ->
                    onMsg.invoke(info.chatMsg)
                    if (info.chatMsg == null) {
                        GroupInfoType.getGroupMsgByMid(info.mid) {
                            info.chatMsg = it
                        }
                    }
                }, onSuccess)
            }
            if (!TextUtils.isEmpty(info.draft) || !TextUtils.isEmpty(info.ref_mid)) {
                onSuccess.invoke(info.draft)
                return
            }
            UserService.get().getCacheSimpleUser(info.last_msg_uid, object : UserSimpleInfoCallback {
                override fun onUserInfoSuccess(userInfo: UserSimpleInfo) {
                    var pref = ""
                    if (userInfo.uid != LoginHelper.getLoginUid()) {
                        pref = userInfo.remarkName + "："
                    }
                    getGroupContent(pref)
                }

                override fun onUserInfoFailed(description: String) {
                    getGroupContent("")
                }
            })
        }
    }

    private object PersonalInfoType : ContentType<WPMessage?>() {
        /**
         * 用于回复的内容显示
         * 不能回复系统消息
         */
        override fun getContent(msg: WPMessage?, onSuccess: (String) -> Unit) {
            if (msg == null) {
                onSuccess.invoke("")
                return
            }
            val info = ConversationInfo()
            info.last_msg_type = msg.media_type ?: 0
            info.last_msg_subtype = msg.subType ?: 0
            info.last_msg_content = msg.content ?: ""
            info.last_msg_uid = msg.send_uid
            getPersonalContentInner(info, {
                it.invoke(msg)
            }, onSuccess)
        }


        /**
         * 根据mid获取单聊消息
         */
        fun getPersonalMsgByMid(
            mid: String,
            onSuccess: (WPMessage?) -> Unit
        ) {
            if (TextUtils.isEmpty(mid)) {
                onSuccess.invoke(null)
                return
            }
            val wpMessage = WPMessage()
            val sql = "select * from ${wpMessage.tableName} where mid = '$mid' limit 1"
            WPStore.queryAsync(wpMessage, sql) { model: WPModel?, _ ->
                onSuccess.invoke(model as WPMessage?)
            }
        }

        /**
         * 单聊消息实际处理内容
         */
        fun getPersonalContentInner(
            info: ConversationInfo,
            onMsg: ((WPMessage?) -> Unit) -> Unit,
            onSuccess: (String) -> Unit
        ) {
            var content = info.last_msg_content
            when (info.last_msg_type) {
                WPMessage.MEDIA_TYPE_TEXT -> {
                    transformPersonalTextContent(info, onMsg) {
                        onSuccess.invoke(TextSpanUtil.getCustomSpanStr(it))
                    }
                }

                WPMessage.MEDIA_TYPE_RED_PACKET_TIP,
                WPMessage.MEDIA_TYPE_RECALL,
                WPMessage.MEDIA_TYPE_SYSTEM -> {
                    onSuccess.invoke(TextSpanUtil.getCustomSpanStr(content))
                }

                WPMessage.MEDIA_TYPE_COCOS_GAME_INVITE -> {
                    onMsg.invoke {
                        if (it != null) {
                            val gameType = it.extensionCocosGameType
                            val name = ConfigHelper.getInstance().getGameConfig(gameType).name
                            onSuccess.invoke("[${ResUtil.getStr(R.string.prop_category_game)}]${name}")
                            return@invoke
                        }
                        onSuccess.invoke(TextSpanUtil.getCustomSpanStr(content))
                    }
                }

                WPMessage.MEDIA_TYPE_INVITE -> {
                    onMsg.invoke {
                        if (it != null) {
                            val cardInfo = InviteCardInfo.build(it.extension)
                            if (cardInfo.inviteType == InviteCardInfo.TYPE_VOICE_INVITE) {
                                onSuccess.invoke("[${ResUtil.getStr(R.string.game_type_voice_room)}]${cardInfo.roomName}")
                                return@invoke
                            }
                        }
                        onSuccess.invoke(TextSpanUtil.getCustomSpanStr(content))
                    }
                }

                WPMessage.MEDIA_TYPE_GAME_INVITE,
                WPMessage.MEDIA_TYPE_XROOM_INVITE -> {
                    content = "[$content]"
                    onSuccess.invoke(TextSpanUtil.getCustomSpanStr(content))
                }

                WPMessage.MEDIA_TYPE_GAME_INVITE_NEW -> {
                    content = if (ShellPackageUtil.hideInviteMsg()) {
                        ""
                    } else {
                        "[$content]"
                    }
                    onSuccess.invoke(TextSpanUtil.getCustomSpanStr(content))
                }

                WPMessage.MEDIA_TYPE_VIP_DONATE -> {
                    content = "[$content]"
                    onSuccess.invoke(TextSpanUtil.getCustomSpanStr(content))
                }

                WPMessage.MEDIA_TYPE_H5_SHARE -> {
                    onMsg.invoke {
                        content =
                            "${ResUtil.getStr(R.string.tab_msg_conversation_item_content_h5_link_tips)}" +
                                    "${MsgH5LinkModel.getTitle(it, content)}"
                        onSuccess.invoke(content)
                    }
                }

                WPMessage.MEDIA_TYPE_GROUP_SHARE -> {
                    onMsg.invoke {
                        content = "[${ResUtil.getStr(R.string.group_chat_title)}]${
                            MsgGroupShareModel.getName(
                                it,
                                content
                            )
                        }"
                        onSuccess.invoke(content)
                    }
                }

                WPMessage.MEDIA_TYPE_PHOTO, WPMessage.MEDIA_TYPE_NORMAL_PHOTO -> {
                    content = if (info.last_msg_subtype == ChatMsg.SUBTYPE_EMOTICON) {
                        ResUtil.getStr(R.string.tab_msg_conversation_item_content_emote_tips)
                    } else if (info.last_msg_type == WPMessage.MEDIA_TYPE_PHOTO) {
                        ResUtil.getStr(R.string.tab_msg_conversation_item_content_private_image_tips)
                    } else {
                        ResUtil.getStr(R.string.tab_msg_conversation_item_content_image_tips)
                    }
                    onSuccess.invoke(content)
                }

                WPMessage.MEDIA_TYPE_AUDIO -> {
                    content = ResUtil.getStr(R.string.tab_msg_conversation_item_content_voice_tips)
                    onSuccess.invoke(content)
                }

                WPMessage.MEDIA_TYPE_CHAT_VIDEO -> {
                    content = ResUtil.getStr(R.string.tab_msg_conversation_item_content_video_tips)
                    onSuccess.invoke(content)
                }

                WPMessage.MEDIA_TYPE_DICE -> {
                    content = ResUtil.getStr(R.string.tab_msg_conversation_item_content_dice_tips)
                    onSuccess.invoke(content)
                }

                WPMessage.MEDIA_TYPE_RED_PACKET -> {
                    content = ResUtil.getStr(R.string.tab_msg_conversation_item_content_rp_tips)
                    onSuccess.invoke(content)
                }

                WPMessage.MEDIA_TYPE_SEND_CARD -> {
                    content =
                        ResUtil.getStr(R.string.tab_msg_conversation_item_content_namecard_tips)
                    onSuccess.invoke(content)
                }

                else -> {
                    content = ResUtil.getStr(R.string.common_room_not_support_msg)
                    onSuccess.invoke(content)
                }
            }
        }

        private fun transformPersonalTextContent(
            info: ConversationInfo,
            onMsg: ((WPMessage?) -> Unit) -> Unit,
            onSuccess: (String) -> Unit
        ) {
            val content = info.last_msg_content
            if (WPMessage.getExtensionSubType(content) == WPMessage.EXTENSION_TYPE_ASSEMBLE_MSG) {
                val inviteCardInfo =
                    WPMessage.getWithInviteCardWithExtension(content)
                if (!TextUtils.isEmpty(inviteCardInfo.sendName)) {
                    val text = ResUtil.getStr(
                        R.string.invite_card_follower_invite,
                        inviteCardInfo.sendName
                    )
                    onSuccess.invoke(text)
                } else {
                    UserService.get().getCacheSimpleUser(
                        inviteCardInfo.sendUid,
                        object : UserSimpleInfoCallback {
                            override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo) {
                                val remarkName = simpleInfo.remarkName
                                val text = ResUtil.getStr(
                                    R.string.invite_card_follower_invite,
                                    remarkName
                                )
                                onSuccess.invoke(text)
                            }

                            override fun onUserInfoFailed(description: String) {
                                val text =
                                    ResUtil.getStr(R.string.invite_card_follower_invite_default)
                                onSuccess.invoke(text)
                            }
                        })
                }
            } else {
                // 如果消息是系统消息，则不可能是礼物消息
                if (UserService.get().isSystemUser(info.last_msg_uid)) {
                    onSuccess.invoke(content)
                    return
                }
                onMsg.invoke {
                    if (it == null) {
                        onSuccess.invoke(content)
                        return@invoke
                    }
                    val type = it.extensionType
                    if (it.isExtensionTypeGift(type)) {
                        val chatGift = MsgTextHelper.NewChatGift.build(it.content)
                        if (chatGift != null) {
                            onSuccess.invoke(
                                "${chatGift.title}，${
                                    ResUtil.getStr(
                                        R.string.chat_gift_msg_receiver_got,
                                        it.giftRecCoinFromExtension
                                    )
                                }"
                            )
                            return@invoke
                        }
                    } else if (it.isExtensionTypeGameSkin(type)) {
                        onSuccess.invoke(
                            ResUtil.getStr(R.string.send_you_one_prop) + ", " +
                                    ResUtil.getStr(
                                        R.string.chat_gift_msg_receiver_got,
                                        it.giftRecCoinFromExtension
                                    )
                        )
                    } else {
                        onSuccess.invoke(content)
                    }
                }
            }
        }
    }

    private object GroupInfoType : ContentType<ChatMsg?>() {
        /**
         * 用于回复的内容显示
         * 不能回复系统消息
         */
        override fun getContent(msg: ChatMsg?, onSuccess: (String) -> Unit) {
            if (msg == null) {
                onSuccess.invoke("")
                return
            }
            val info = ConversationInfo()
            info.last_msg_type = msg.mediaType
            info.last_msg_subtype = msg.subType
            info.last_msg_content = msg.content ?: ""
            info.last_msg_uid = msg.send_uid
            getGroupContentInner(info, "", {
                it.invoke(msg)
            }, onSuccess)
        }

        /**
         * 根据mid获取群聊消息
         */
        fun getGroupMsgByMid(
            mid: String,
            onSuccess: (ChatMsg?) -> Unit
        ) {
            if (TextUtils.isEmpty(mid)) {
                onSuccess.invoke(null)
                return
            }
            val groupChatMsg = GroupChatMsg()
            val sql = "select * from ${groupChatMsg.tableName} where mid = '${mid}' limit 1"
            WPStore.queryAsync(groupChatMsg, sql) { model: WPModel?, _ ->
                onSuccess.invoke(model as ChatMsg?)
            }
        }

        /**
         * 群聊消息实际处理内容
         */
        fun getGroupContentInner(
            info: ConversationInfo,
            pref: String,
            onMsg: ((ChatMsg?) -> Unit) -> Unit,
            onSuccess: (String) -> Unit
        ) {
            var content = info.last_msg_content
            when (info.last_msg_type) {
                ChatMsg.MEDIA_TYPE_GRAB_RED_PACKET -> {
                    content = pref + TextSpanUtil.getCustomSpanStr(content)
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_TEXT -> {
                    transformGroupTextContent(content, onMsg) {
                        onSuccess.invoke(TextSpanUtil.getCustomSpanStr(pref + it))
                    }
                }

                ChatMsg.MEDIA_TYPE_GIFT -> {
                    transformGroupGift(pref + content, onMsg, onSuccess)
                }

                ChatMsg.MEDIA_TYPE_ALL_GIFT_VALUE -> {
                    transformGroupMultiGift(pref + content, onMsg, onSuccess)
                }

                ChatMsg.MEDIA_TYPE_AUDIO -> {
                    content =
                        pref + ResUtil.getStr(R.string.tab_msg_conversation_item_content_voice_tips)
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_VIDEO -> {
                    content =
                        pref + ResUtil.getStr(R.string.tab_msg_conversation_item_content_video_tips)
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_DICE -> {
                    content =
                        pref + ResUtil.getStr(R.string.tab_msg_conversation_item_content_dice_tips)
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_CARD_VALUE -> {
                    content =
                        pref + ResUtil.getStr(R.string.tab_msg_conversation_item_content_namecard_tips)
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_COCOS_GAME_INVITE -> {
                    content = pref + try {
                        val gameInviteInfo = JsonUtil.getGson().fromJson(
                            content,
                            GameInviteInfo::class.java
                        )
                        ResUtil.getStr(
                            R.string.tab_msg_conversation_item_content_invation_tips,
                            gameInviteInfo.gameTitle
                        )
                    } catch (_: Exception) {
                        ResUtil.getStr(R.string.common_room_not_support_msg)
                    }
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_SYSTEM -> {
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_IMAGE -> {
                    content = pref + if (info.last_msg_subtype == ChatMsg.SUBTYPE_EMOTICON) {
                        ResUtil.getStr(R.string.tab_msg_conversation_item_content_emote_tips)
                    } else {
                        ResUtil.getStr(R.string.tab_msg_conversation_item_content_image_tips)
                    }
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_RED_PACKET -> {
                    content =
                        pref + ResUtil.getStr(R.string.tab_msg_conversation_item_content_rp_tips)
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_FAMILY -> {
                    content = "$pref[${ResUtil.getStr(R.string.family_box_text)}]"
                    onSuccess.invoke(content)
                }

                ChatMsg.MEDIA_TYPE_H5_SHARE -> {
                    onMsg.invoke {
                        content = pref +
                                ResUtil.getStr(R.string.tab_msg_conversation_item_content_h5_link_tips) +
                                MsgH5LinkModel.getTitle(it, content)
                        onSuccess.invoke(content)
                    }
                }

                ChatMsg.MEDIA_TYPE_SHARE_GROUP -> {
                    onMsg.invoke {
                        content = "$pref[${ResUtil.getStr(R.string.group_chat_title)}]${
                            MsgGroupShareModel.getName(
                                it,
                                content
                            )
                        }"
                        onSuccess.invoke(content)
                    }
                }

                else -> {
                    content = pref + ResUtil.getStr(R.string.common_room_not_support_msg)
                    onSuccess.invoke(content)
                }
            }
        }

        private fun transformGroupGift(
            content: String,
            onMsg: ((ChatMsg?) -> Unit) -> Unit,
            onSuccess: (String) -> Unit
        ) {
            onMsg.invoke {
                if (it == null) {
                    onSuccess.invoke(content)
                    return@invoke
                }
                val chatGift = MsgTextHelper.NewChatGift.build(it.content)
                if (chatGift != null) {
                    var giveAwayDesc = ""
                    try {
                        val jsonObject: JSONObject = JSONObject(it.ext)
                        giveAwayDesc = jsonObject.optString("give_away_desc")
                    } catch (e: JSONException) {
                        e.printStackTrace()
                    }
                    onSuccess.invoke(
                        "${chatGift.title}，${
                            ResUtil.getStr(
                                R.string.chat_gift_msg_receiver_got,
                                giveAwayDesc
                            )
                        }"
                    )
                    return@invoke
                }
            }
        }

        private fun transformGroupMultiGift(
            content: String,
            onMsg: ((ChatMsg?) -> Unit) -> Unit,
            onSuccess: (String) -> Unit
        ) {
            onMsg.invoke {
                if (it == null) {
                    onSuccess.invoke(content)
                    return@invoke
                }
                if (it is GroupChatMsg) {
                    val giftInfo = GroupChatMsg.getMulGiftInfoFromExt(it)
                    val gift = ConfigHelper.getInstance().giftConfig.getGift(giftInfo.giftId)
                    val giftName = if (gift != null) {
                        gift.name
                    } else {
                        ResUtil.getStr(R.string.unidentified_gift)
                    }
                    val giftUnit = if (gift != null) gift.unit else " "
                    val receiver = if (giftInfo.isGroup) ResUtil.getResource()
                        .getString(R.string.group_mul_gift_mic) else if (giftInfo.isFamily) ResUtil.getResource()
                        .getString(R.string.family_mul_gift_mic) else ResUtil.getStr(R.string.unknown_person)
                    val totalNum = giftInfo.giftNum * giftInfo.list.size
                    val sendTip = ResUtil.getQuantityStr(
                        R.plurals.gift_common_send_tip_G_new,
                        2,
                        receiver,
                        totalNum,
                        giftUnit,
                        giftName
                    )
                    onSuccess.invoke(
                        "${sendTip}，${
                            ResUtil.getResource().getString(
                                R.string.gift_total_receive_tip_new,
                                giftInfo.totalDesc
                            )
                        }"
                    )
                }
            }
        }

        private fun transformGroupTextContent(
            content: String,
            onMsg: ((ChatMsg?) -> Unit) -> Unit,
            onSuccess: (String) -> Unit
        ) {
            onMsg.invoke {
                if (it == null) {
                    onSuccess.invoke(content)
                    return@invoke
                }
                if (!it.isInviteMsg || it !is GroupChatMsg) {
                    onSuccess.invoke(content)
                    return@invoke
                }
                val cardInfo = InviteCardInfo.build(it.ext)
                val newContent = when (cardInfo.inviteType) {
                    InviteCardInfo.TYPE_GAME_INVITE -> {
                        val gameConfig = ConfigHelper.getInstance().getGameConfig(cardInfo.gameType)
                        "[${ResUtil.getStr(R.string.prop_category_game)}]${gameConfig.name}"
                    }

                    InviteCardInfo.TYPE_VOICE_INVITE -> {
                        "[${ResUtil.getStr(R.string.game_type_voice_room)}]${cardInfo.roomName}"
                    }

                    else -> {
                        content
                    }
                }
                onSuccess.invoke(newContent)
            }
        }
    }
}