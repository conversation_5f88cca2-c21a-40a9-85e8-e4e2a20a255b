package com.wepie.wespy.module.report.module.message;

import android.text.TextUtils;

import com.google.android.gms.common.util.CollectionUtils;
import com.huiwan.store.database.WPStore;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.helper.ChatAlbumHelper;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;
import com.wepie.wespy.model.entity.hwroom.HWRoomMsg;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.chat.manager.ChatManager;
import com.wepie.wespy.module.chat.model.ChatImage;
import com.wepie.wespy.module.chat.send.ImageSizeCache;
import com.wepie.wespy.module.chat.send.face.ImageSize;
import com.wepie.wespy.module.report.ReportBuilder;
import com.wepie.wespy.module.report.ReportConst;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by bigwen on 2020-05-19.
 */
public class ReportChatUtil {

    private static final int MSG_NUM_PER_PAGE = 200;
    private static final int MAX_LENGTH = 110;

    public static void loadFromSingleChat(int chatUid, long oldestMsgTimeStamp, ReportLoadChatCallback callback) {
        loadFromSingleChat(chatUid, oldestMsgTimeStamp, new ArrayList<>(), callback);
    }

    private static void loadFromSingleChat(int chatUid, long oldestMsgTimeStamp, List<ReportChatMsg> cacheList, ReportLoadChatCallback callback) {
        String table = new WPMessage().getTableName();
        int curUid = LoginHelper.getLoginUid();
        String sql = "select * from " + table
                + " where ( (recv_uid=" + chatUid + " and send_uid = " + curUid + ") "
                + " or (send_uid=" + chatUid + " and recv_uid = " + curUid + ") ) "
                + " and time < " + oldestMsgTimeStamp
                + " and ( media_type = 1 or media_type = 12 or media_type = 3 or media_type = 25 ) "
                + " order by time desc limit "+ MSG_NUM_PER_PAGE;

        WPStore.fetchListAsync(new WPMessage(), sql, new WPStore.WPModelListCallback<WPMessage>() {
            @Override
            public void onResult(List<WPMessage> list, Exception e) {
                if (CollectionUtils.isEmpty(list)) {
                    if (callback != null) callback.onResult(cacheList);
                    return;
                }

                List<WPMessage> newList = new ArrayList<>();
                for (int i = list.size() - 1; i >= 0; i--) {
                    newList.add(list.get(i));
                }

                long curMsgTimeStamp = oldestMsgTimeStamp;
                if (!newList.isEmpty()) {
                    curMsgTimeStamp = newList.get(0).getTime();
                }

                List<ReportChatMsg> chatList = transferSingleChat(newList);
                cacheList.addAll(0, chatList);
                if (cacheList.size() >= 110) {
                    if (callback != null) callback.onResult(cacheList.subList(cacheList.size() - MAX_LENGTH, cacheList.size()));
                } else {
                    loadFromSingleChat(chatUid, curMsgTimeStamp, cacheList, callback);
                }
            }
        });
    }

    private static List<ReportChatMsg> transferSingleChat(List<WPMessage> messageList) {
        List<ReportChatMsg> dataList = new ArrayList<>();
        int selfUid = LoginHelper.getLoginUid();
        for (WPMessage wpMessage : messageList) {
            if (wpMessage.getMedia_type() == WPMessage.MEDIA_TYPE_TEXT
                    || wpMessage.getMedia_type() == WPMessage.MEDIA_TYPE_NORMAL_PHOTO
                    || wpMessage.getMedia_type() == WPMessage.MEDIA_TYPE_CHAT_VIDEO
                    || wpMessage.getMedia_type() == WPMessage.MEDIA_TYPE_AUDIO) {
                if (wpMessage.getMedia_type() == WPMessage.MEDIA_TYPE_TEXT && (wpMessage.isExtensionTypeGift(wpMessage.getExtensionType()) || wpMessage.isExtensionTypeGameSkin(wpMessage.getExtensionType()))) {
                    continue;
                }
                dataList.add(fromWPMessage(selfUid, wpMessage));
            }
        }
        return dataList;
    }

    public static ReportChatMsg loadSingleChatFromMid(String mid) {
        WPMessage msg = new WPMessage();
        String sql = "select * from " + msg.getTableName() + " where mid = '" + mid + "'";
        List<WPMessage> selected = WPStore.getInstance().fetchListSync(msg, sql);
        if (selected.isEmpty()) {
            return null;
        } else {
            return fromWPMessage(LoginHelper.getLoginUid(), selected.get(0));
        }
    }

    private static ReportChatMsg fromWPMessage(int selfUid, WPMessage wpMessage) {
        ReportChatMsg reportChatMsg = new ReportChatMsg();
        reportChatMsg.setReportScene(ReportChatMsg.SINGLE_CHAT);
        reportChatMsg.setItemType(selfUid == wpMessage.getSend_uid() ? ReportChatInterface.ITEM_TYPE_CHAT_SELF : ReportChatInterface.ITEM_TYPE_CHAT_OTHER);
        reportChatMsg.setOriginalMsgType(wpMessage.getMedia_type());
        reportChatMsg.setUid(wpMessage.getSend_uid());
        reportChatMsg.setTime(wpMessage.getTime());
        if (wpMessage.isVideoMsg()) {
            reportChatMsg.setMsg(wpMessage.getExtension());
        } else {
            reportChatMsg.setMsg(wpMessage.getContent());
        }
        reportChatMsg.setMid(wpMessage.getMid());
        reportChatMsg.setStatus(wpMessage.getStatus());
        return reportChatMsg;
    }

    public static void loadGroupChat(int group_id, long oldestMsgTimeStamp, ReportLoadChatCallback callback) {
        loadGroupChat(group_id, oldestMsgTimeStamp, new ArrayList<>(), callback);
    }

    private static void loadGroupChat(int group_id, long oldestMsgTimeStamp, List<ReportChatMsg> cacheList, ReportLoadChatCallback callback) {
        GroupChatMsg groupChatMsg = new GroupChatMsg();
        String sql = "select * from " + groupChatMsg.getTableName()
                + " where group_id = " + group_id
                + " and time < " + oldestMsgTimeStamp
                + " and ( mediaType = 1 or mediaType = 2 or mediaType = 3 or mediaType = 16 ) "
                + " order by time desc limit " + MSG_NUM_PER_PAGE;
        WPStore.fetchListAsync(groupChatMsg, sql, new WPStore.WPModelListCallback<GroupChatMsg>() {
            @Override
            public void onResult(List<GroupChatMsg> list, Exception e) {
                if(CollectionUtils.isEmpty(list)) {
                    if (callback != null) callback.onResult(cacheList);
                    return;
                }
                int size = list.size();
                ArrayList<GroupChatMsg> msgArray = new ArrayList<>(list.size());
                for (int i = size - 1; i >= 0; i--) {
                    GroupChatMsg wpModel = list.get(i);
                    if (wpModel != null) {
                        msgArray.add(wpModel);
                    }
                }

                long curMsgTimeStamp = oldestMsgTimeStamp;
                if (!msgArray.isEmpty()) {
                    curMsgTimeStamp = msgArray.get(0).getTime();
                }

                List<ReportChatMsg> chatList = transferGroupChat(msgArray);
                cacheList.addAll(0, chatList);
                if (cacheList.size() >= 110) {
                    if (callback != null) callback.onResult(cacheList.subList(cacheList.size() - MAX_LENGTH, cacheList.size()));
                } else {
                    loadGroupChat(group_id, curMsgTimeStamp, cacheList, callback);
                }
            }
        });
    }

    public static ReportChatMsg loadGroupChatFromMid(String mid) {
        GroupChatMsg msg = new GroupChatMsg();
        String sql = "select * from " + msg.getTableName() + " where mid = '" + mid + "'";
        List<GroupChatMsg> selected = WPStore.getInstance().fetchListSync(msg, sql);
        if (selected.isEmpty()) {
            return null;
        } else {
            return fromGroupChatMsg(LoginHelper.getLoginUid(), selected.get(0));
        }
    }

    private static List<ReportChatMsg> transferGroupChat(List<GroupChatMsg> messageList) {
        List<ReportChatMsg> dataList = new ArrayList<>();
        int selfUid = LoginHelper.getLoginUid();
        for (GroupChatMsg groupChatMsg: messageList) {
            if (groupChatMsg.getMediaType() == GroupChatMsg.MEDIA_TYPE_TEXT
                    || groupChatMsg.getMediaType() == GroupChatMsg.MEDIA_TYPE_IMAGE
                    || groupChatMsg.getMediaType() == GroupChatMsg.MEDIA_TYPE_VIDEO
                    || groupChatMsg.getMediaType() == GroupChatMsg.MEDIA_TYPE_AUDIO) {
                if (groupChatMsg.getMediaType() == GroupChatMsg.MEDIA_TYPE_TEXT && (groupChatMsg.getSubType() != 0)) {
                    continue;
                }
                dataList.add(fromGroupChatMsg(selfUid, groupChatMsg));
            }
        }
        return dataList;
    }

    private static ReportChatMsg fromGroupChatMsg(int selfUid, GroupChatMsg groupChatMsg) {
        ReportChatMsg reportChatMsg = new ReportChatMsg();
        reportChatMsg.setReportScene(ReportChatMsg.GROUP_CHAT);
        reportChatMsg.setItemType(selfUid == groupChatMsg.getSend_uid() ? ReportChatInterface.ITEM_TYPE_CHAT_SELF : ReportChatInterface.ITEM_TYPE_CHAT_OTHER);
        reportChatMsg.setOriginalMsgType(groupChatMsg.getMediaType());
        reportChatMsg.setUid(groupChatMsg.getSend_uid());
        reportChatMsg.setTime(groupChatMsg.getTime());
        if (groupChatMsg.isVideoMsg()) {
            reportChatMsg.setMsg(groupChatMsg.getExt());
        } else {
            reportChatMsg.setMsg(groupChatMsg.getContent());
        }
        reportChatMsg.setMid(groupChatMsg.getMid());
        reportChatMsg.setStatus(groupChatMsg.getStatus());
        return reportChatMsg;
    }

    public static List<ReportChatMsg> transferVoiceRoom(String mid) {
        List<VoiceRoomMsg> voiceRoomMsgList = new ArrayList<>(VoiceRoomService.getInstance().getMsgArray());
        List<ReportChatMsg> dataList = new ArrayList<>();
        int index = 0;
        int chooseIndex = 0;
        for (VoiceRoomMsg msg: voiceRoomMsgList) {
            int mediaType = msg.getMediaType();
            if (mediaType == VoiceRoomMsg.MEDIA_TYPE_TEXT || mediaType == VoiceRoomMsg.MEDIA_TYPE_EMOJI
                    || mediaType == VoiceRoomMsg.MEDIA_TYPE_PHOTO) {
                if (UserService.get().isSystemUser(msg.getSend_uid())) continue;
                ReportChatMsg reportChatMsg = new ReportChatMsg();
                reportChatMsg.setReportScene(ReportChatMsg.VOICE_ROOM);
                reportChatMsg.setItemType(ReportChatInterface.ITEM_TYPE_PUBLIC_SCREEN);
                reportChatMsg.setOriginalMsgType(msg.getMediaType());
                reportChatMsg.setUid(msg.getSend_uid());
                reportChatMsg.setTime(msg.getTime());
                if (mediaType == VoiceRoomMsg.MEDIA_TYPE_EMOJI
                        || mediaType == VoiceRoomMsg.MEDIA_TYPE_PHOTO) {
                    String content = msg.getMsgContent();
                    ChatImage chatImage;
                    if (mediaType == VoiceRoomMsg.MEDIA_TYPE_EMOJI) {
                        chatImage = ChatImage.emoticon(content, "emoticon");
                    } else {
                        ImageSize size = ImageSize.from(content);
                        if (!size.sizeValid()) {
                            ImageSizeCache.get().getImageSize(content, size);
                        }
                        chatImage = new ChatImage(content, size.getRatio());
                    }
                    reportChatMsg.setMsg(chatImage.toMsgString());
                } else {
                    reportChatMsg.setMsg(msg.getMsgContent());
                }
                reportChatMsg.setMid(msg.getMid());
                if (!TextUtils.isEmpty(mid) && mid.equals(msg.getMid())) {
                    reportChatMsg.setChecked(true);
                    chooseIndex = index;
                }
                dataList.add(reportChatMsg);
                index++;
            }
        }
        int startIndex = chooseIndex - 50;
        int endIndex = chooseIndex + 50;
        if (startIndex < 0) startIndex = 0;
        if (endIndex > dataList.size()) endIndex = dataList.size();
        return dataList.subList(startIndex, endIndex);
    }

    private static List<FixRoomMsg> fixRoomMsgList = new ArrayList<>();
    public static void setFixRoomMsgList(List<FixRoomMsg> dataList) {
        fixRoomMsgList.clear();
        if (dataList != null) {
            fixRoomMsgList.addAll(dataList);
        }
    }

    public static void clearFixRoomMsg() {
        fixRoomMsgList.clear();
    }

    public static List<ReportChatMsg> transferFixRoom(String mid) {
        List<ReportChatMsg> dataList = new ArrayList<>();
        int selfUid = LoginHelper.getLoginUid();
        int index = 0;
        int chooseIndex = 0;
        for (FixRoomMsg fixRoomMsg: fixRoomMsgList) {
            if (fixRoomMsg.getMediaType() == FixRoomMsg.MEDIA_TYPE_TEXT) {
                if (UserService.get().isSystemUser(fixRoomMsg.getSend_uid())) continue;
                ReportChatMsg reportChatMsg = new ReportChatMsg();
                reportChatMsg.setReportScene(ReportChatMsg.FIX_ROOM);
                reportChatMsg.setItemType(selfUid == fixRoomMsg.getSend_uid() ? ReportChatInterface.ITEM_TYPE_CHAT_SELF : ReportChatInterface.ITEM_TYPE_CHAT_OTHER);
                reportChatMsg.setOriginalMsgType(fixRoomMsg.getMediaType());
                reportChatMsg.setUid(fixRoomMsg.getSend_uid());
                reportChatMsg.setTime(fixRoomMsg.getTime());
                reportChatMsg.setMsg(fixRoomMsg.getContent());
                reportChatMsg.setMid(fixRoomMsg.getMid());
                if (!TextUtils.isEmpty(mid) && mid.equals(fixRoomMsg.getMid())) {
                    reportChatMsg.setChecked(true);
                    chooseIndex = index;
                }
                dataList.add(reportChatMsg);
                index++;
            }
        }
        int startIndex = chooseIndex - 50;
        int endIndex = chooseIndex + 50;
        if (startIndex < 0) startIndex = 0;
        if (endIndex > dataList.size()) endIndex = dataList.size();
        return dataList.subList(startIndex, endIndex);
    }

    private static List<HWRoomMsg> hwRoomMsgList = new ArrayList<>();
    public static void setHWRoomMsgList(List<HWRoomMsg> dataList) {
        hwRoomMsgList.clear();
        if (dataList != null) {
            hwRoomMsgList.addAll(dataList);
        }
    }

    public static void clearHWRoomMsg() {
        hwRoomMsgList.clear();
    }

    public static List<ReportChatMsg> transferHWRoom(String mid) {
        List<ReportChatMsg> dataList = new ArrayList<>();
        int selfUid = LoginHelper.getLoginUid();
        int index = 0;
        int chooseIndex = 0;
        for (HWRoomMsg hwRoomMsg: hwRoomMsgList) {
            if (hwRoomMsg.getMsgType() == HWRoomMsg.MSG_TYPE_TEXT) {
                if (UserService.get().isSystemUser(hwRoomMsg.getSender())) continue;
                ReportChatMsg reportChatMsg = new ReportChatMsg();
                reportChatMsg.setReportScene(ReportChatMsg.HW_ROOM);
                reportChatMsg.setItemType(selfUid == hwRoomMsg.getSender() ? ReportChatInterface.ITEM_TYPE_CHAT_SELF : ReportChatInterface.ITEM_TYPE_CHAT_OTHER);
                reportChatMsg.setOriginalMsgType(hwRoomMsg.getMsgType());
                reportChatMsg.setUid(hwRoomMsg.getSender());
                reportChatMsg.setTime(hwRoomMsg.getSendTime());
                reportChatMsg.setMsg(hwRoomMsg.getContent());
                reportChatMsg.setMid(hwRoomMsg.getMid());
                if (!TextUtils.isEmpty(mid) && mid.equals(hwRoomMsg.getMid())) {
                    reportChatMsg.setChecked(true);
                    chooseIndex = index;
                }
                dataList.add(reportChatMsg);
                index++;
            }
        }
        int startIndex = chooseIndex - 50;
        int endIndex = chooseIndex + 50;
        if (startIndex < 0) startIndex = 0;
        if (endIndex > dataList.size()) endIndex = dataList.size();
        return dataList.subList(startIndex, endIndex);
    }

    public static void eventReportChatEmoticon(ReportBuilder reportBuilder) {
        int reportType = reportBuilder.getReportType();
        String reason = reportBuilder.getReportReason();
        String mid = reportBuilder.getEmoticonMid();
        Map<String, Object> eventInfo = new HashMap<>();
        eventInfo.put("reason", reason);
        if (reportType == ReportConst.ReportTypePrivateChat) {
            WPMessage msg = ChatManager.getInstance().getMsg(mid);
            if (msg != null && msg.getMedia_type() == WPMessage.MEDIA_TYPE_NORMAL_PHOTO) {
                ChatImage image = ChatImage.fromString(msg.getContent());
                eventInfo.put("url", image.url);
                eventInfo.put("target_uid", msg.getRecv_uid());
                ShenceEvent.appClickSource(TrackSource.SINGLE_CHAT, TrackButtonName.REPORT_EMOTICON, eventInfo);
            }
        } else if (reportType == ReportConst.ReportTypeGroup) {
            ChatAlbumHelper.fetchGroupChatMsgWithMid(mid, data -> {
                ChatImage image = ChatImage.fromString(data.getContent());
                eventInfo.put("url", image.url);
                eventInfo.put("target_uid", data.getSend_uid());
                ShenceEvent.appClickSource(TrackSource.GROUP_CHAT, TrackButtonName.REPORT_EMOTICON, eventInfo);
            });
        }
    }
}
