package com.wepie.wespy.module.voiceroom.seat;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.huiwan.user.UserInfoLoadCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISeatIntimacyImageView;

public class SimpleSeatIntimacyImageView extends PluginFrameLayout implements ISeatIntimacyImageView {
    private ImageView seatIntimacyIcon;
    private boolean isOwner = false;
    protected int leftSeatNum;
    protected int rightSeatNum;

    private int currentLeftUid;
    private int currentRightUid;
//    private boolean isInited = false;


    public SimpleSeatIntimacyImageView(Context context) {
        this(context, null, 0);
    }

    public SimpleSeatIntimacyImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SimpleSeatIntimacyImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs);

        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SeatIntimacyImageView, defStyle, 0);
        isOwner = typedArray.getBoolean(R.styleable.SeatIntimacyImageView_isOwner, false);
        leftSeatNum = typedArray.getInt(R.styleable.SeatIntimacyImageView_leftNum, 0);
        rightSeatNum = typedArray.getInt(R.styleable.SeatIntimacyImageView_rightNum, 0);
        typedArray.recycle();
    }

    @Override
    protected void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.room_simple_seat_intimacy_icon_view, this);

        seatIntimacyIcon = findViewById(R.id.game_head_heart_icon);
    }

    @Override
    protected void initData() {
        VoiceRoomInfo roomInfo = getMainPlugin().getRoomInfo();
        update(roomInfo, RoomInfoUpdateEvent.ALL);
    }

    @Override
    public void update(VoiceRoomInfo roomInfo, RoomInfoUpdateEvent event) {
        if (roomInfo == null || roomInfo.getIntimateSeatInfo() == null) {
            setVisibility(View.GONE);
            return;
        }

        if (event.features != RoomInfoUpdateEvent.F_ALL && event.seatNum != leftSeatNum && event.seatNum != rightSeatNum) {
            return;
        }

        if (isOwner) {
            if (roomInfo.getIntimateSeatInfo().isOwnerIsOpen()) {
                setVisibility(View.VISIBLE);

                updateIntimacyIcon(roomInfo);
            } else {
                setVisibility(View.GONE);
            }
        } else {
            if (roomInfo.getIntimateSeatInfo().isNormalIsOpen()) {
                setVisibility(View.VISIBLE);

                updateIntimacyIcon(roomInfo);
            } else {
                setVisibility(View.GONE);
            }
        }
    }

    public void updateIntimacyIcon(VoiceRoomInfo roomInfo) {
        VoiceRoomInfo.SeatInfo leftSeatInfo = roomInfo.getSeatInfoByNum(leftSeatNum);
        VoiceRoomInfo.SeatInfo rightSeatInfo = roomInfo.getSeatInfoByNum(rightSeatNum);

        //先判断是否为空
        if (leftSeatInfo == null || rightSeatInfo == null) {
            return;
        }
        if (currentLeftUid == leftSeatInfo.uid && currentRightUid == rightSeatInfo.uid) {
            return;
        }

        currentLeftUid = leftSeatInfo.uid;
        currentRightUid = rightSeatInfo.uid;

        if (leftSeatInfo.isEmpty() || rightSeatInfo.isEmpty()
                || leftSeatInfo.isSealed() || rightSeatInfo.isSealed()) {
            seatIntimacyIcon.setImageResource(R.drawable.intimacy_heart_gray_icon);
        } else {
            getServerUserInfo(leftSeatInfo.uid, rightSeatInfo.uid);
        }
    }

    private void getServerUserInfo(int leftUid, int rightUid) {
        UserService.get().getCacheUserFromServer(leftUid, new UserInfoLoadCallback() {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                if (seatIntimacyIcon == null) {
                    return;
                }

                if (leftUid != currentLeftUid || rightUid != currentRightUid) {
                    return;
                }

                if (userInfo == null) {
                    seatIntimacyIcon.setImageResource(R.drawable.intimacy_heart_white_icon);
                    return;
                }

                if (rightUid > 0 && userInfo.getMateUid() > 0 && rightUid == userInfo.getMateUid()) {
                    seatIntimacyIcon.setImageResource(R.drawable.intimacy_heart_icon);
                } else {
                    seatIntimacyIcon.setImageResource(R.drawable.intimacy_heart_white_icon);
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                HLog.d("updateIntimacyIcon", "getServerUserInfo fail: " + description);

                if (seatIntimacyIcon == null) {
                    return;
                }
                seatIntimacyIcon.setImageResource(R.drawable.intimacy_heart_white_icon);
            }
        });
    }
}
