package com.wepie.wespy.module.login.start

import androidx.lifecycle.lifecycleScope
import com.huiwan.base.util.InitializerManagerUtils
import com.wejoy.weplay.ex.GlobalLife
import com.wepie.skynet.apm.Apm
import com.wepie.startup.InitializerCallback
import com.wepie.startup.impl.TaskData
import com.wepie.wespy.base.appfyers.AppsFlyerUtil
import com.wepie.wespy.helper.shence.InstallChannelUtil
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.helper.shence.ShenceUtil
import com.wepie.wespy.module.login.launch.LaunchCenter
import com.wepie.wespy.module.login.launch.LaunchUiEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 逻辑处理调整位置，不在 Activity 内部处理
 */
class StartActivityDelegate(private val activity: StartActivity) {

    /**
     * 观察 UI 事件。
     */
    fun observer() {
        activity.lifecycleScope.launch {
            LaunchCenter.uiEvent.collectLatest {
                Apm.recordTimePoint("LaunchUiEvent{${it.desc}}")
                when (it) {
                    LaunchUiEvent.JumpToLogin -> {
                        activity.jumpLogin()
                    }

                    LaunchUiEvent.JumpToAd -> {
                        activity.jumpAd()
                    }

                    LaunchUiEvent.JumpToCompleteUserInfo -> {
                        activity.jumpCompleteUserInfo()
                    }

                    LaunchUiEvent.JumpToMain -> {
                        activity.jumpMain()
                    }

                    is LaunchUiEvent.ErrShowDialog -> {
                        activity.showUpdateFailed(it.globalError, it.msg)
                    }
                }
            }
        }
    }

    /**
     * 启动时的 trace 任务
     */
    fun handleTraceEvents() {
        InstallChannelUtil.setInstallChannel()
        InitializerManagerUtils.addCallback(GlobalLife, TrackStartInitializerCallback())
    }
}

internal class TrackStartInitializerCallback : InitializerCallback {
    override fun onFinish(list: List<TaskData>) {
        CoroutineScope(Dispatchers.IO).launch {
            ShenceUtil.enableDataCollect()
            AppsFlyerUtil.trackAppStart()
            ShenceEvent.simInfoSubmit()
        }
        InitializerManagerUtils.removeCallback(this)
    }

    override fun tags(): List<Any> = listOf("AppsFlyer")
}