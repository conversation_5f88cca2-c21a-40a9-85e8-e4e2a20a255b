package com.wepie.wespy.module.shop.activity;

import static androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.collection.IFilter;
import com.huiwan.base.util.collection.ListWrapper;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.marry.MyPropInfo;
import com.wepie.wespy.module.shop.MyPropAdapter;
import com.wepie.wespy.module.shop.MyPropItem;
import com.wepie.wespy.module.shop.MyPropManager;
import com.wepie.wespy.module.shop.MyPropSpanSizeLookUp;
import com.wepie.wespy.module.shop.ShopRecycleDivider;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by three on 15/9/28.
 */
public class MyPropActivity extends BaseActivity {
    private RecyclerView propRecyclerView;
    private HWUIEmptyView emptyView;
    private View topPadding;
    private final Set<Integer> itemsNew = new HashSet<>();
    private final Set<Integer> rpNew = new HashSet<>();
    private final MyPropAdapter adapter = new MyPropAdapter();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_my_prop);
        initView();
    }

    private void initView() {
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        emptyView = findViewById(R.id.my_prop_empty_view);
        topPadding = findViewById(R.id.top_padding);

        actionBar.addTitleAndBack(getString(R.string.my_bag));
        propRecyclerView = findViewById(R.id.my_prop_list);

        GridLayoutManager layoutManager = new GridLayoutManager(this, 3);
        adapter.setCallback(() -> refreshServerData(itemsNew, rpNew));
        layoutManager.setSpanSizeLookup(new MyPropSpanSizeLookUp(adapter));
        propRecyclerView.setLayoutManager(layoutManager);
        propRecyclerView.setAdapter(adapter);
        propRecyclerView.addItemDecoration(new ShopRecycleDivider(ScreenUtil.dip2px(15),
                this, true));
        propRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == SCROLL_STATE_IDLE) {
                    adapter.resumeRingAnim();
                } else {
                    adapter.pauseRingAnim();
                }
            }
        });
        List<Integer> item = RedDotUtil.get().getUpdatedData(RedDotInfo.RED_DOT_BAG);
        if (item != null && itemsNew.isEmpty() && !item.isEmpty()) {
            itemsNew.addAll(item);
        }
        item = RedDotUtil.get().getUpdatedData(RedDotInfo.RED_DOT_BAG_RP);
        if (item != null && rpNew.isEmpty() && !item.isEmpty()) {
            rpNew.addAll(item);
        }
        RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_BAG);
        RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_BAG_RP);
        refreshLocalData(itemsNew, rpNew);
        refreshServerData(itemsNew, rpNew);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (adapter != null) adapter.pauseRingAnim();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (adapter != null) adapter.resumeRingAnim();
    }

    private void refreshLocalData(Set<Integer> itemsNew, Set<Integer> rpNew) {
        ArrayList<MyPropInfo> propList = MyPropManager.getInstance().getLocalPropList();
        if (propList.size() > 0) update(propList, itemsNew, rpNew);
    }

    private void refreshServerData(final Set<Integer> itemsNew, final Set<Integer> rpNew) {
        MyPropManager.getInstance().getPropList(new LifeDataCallback<List<MyPropInfo>>(this) {
            @Override
            public void onSuccess(Result<List<MyPropInfo>> result) {
                update(result.data, itemsNew, rpNew);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void update(List<MyPropInfo> propList, Set<Integer> itemsNew, Set<Integer> rpNew) {
        List<MyPropInfo> filteredList = new ListWrapper<>(propList).filter(filter);
        if (filteredList.isEmpty()) {
            emptyView.setType(HWUIEmptyView.base_empty_nothing);
            emptyView.setVisibility(View.VISIBLE);
            topPadding.setVisibility(View.VISIBLE);
            propRecyclerView.setVisibility(View.INVISIBLE);
        } else {
            emptyView.setVisibility(View.GONE);
            topPadding.setVisibility(View.GONE);
            propRecyclerView.setVisibility(View.VISIBLE);
            updateItemData(filteredList, itemsNew, rpNew);
        }
    }

    private void updateItemData(List<MyPropInfo> propList, Set<Integer> itemsNew, Set<Integer> rpNew) {
        Map<Integer, List<MyPropItem>> map = new ArrayMap<>();
        for (MyPropInfo propInfo : propList) {
            if (propInfo.isRedPacket()) {
                MyPropItem myPropItem = new MyPropItem();
                boolean newRp = rpNew != null && rpNew.contains(propInfo.getRedPacketStuffId());
                myPropItem.initItem(propInfo, newRp);
                addItemData(map, PropItem.CATEGORY_ITEM, myPropItem);
            } else {
                addItemData(propInfo, itemsNew, map);
            }
        }
        adapter.refresh(map);
    }

    private void addItemData(MyPropInfo propInfo, Set<Integer> newItems, Map<Integer, List<MyPropItem>> map) {
        PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(propInfo.prop_id);
        // 游戏皮肤套装不直接展示在背包里面，而是通过散件的方式展示
        if (item == null || item.getType() == PropItem.TYPE_JACKAROO_SKIN_SET) {
            return;
        }
        int category;
        if (item.isMarking()) {
            category = PropItem.CATEGORY_DECORATION;
        } else {
            category = item.getCategoryType();
        }

        MyPropItem myPropItem = new MyPropItem();
        boolean newItem = newItems != null && newItems.contains(propInfo.prop_id);
        myPropItem.initItem(propInfo, newItem);
        addItemData(map, category, myPropItem);
    }

    private void addItemData(Map<Integer, List<MyPropItem>> map, int category, MyPropItem myPropItem) {
        List<MyPropItem> list = map.get(category);
        if (list == null) {
            list = new ArrayList<>();
            map.put(category, list);
        }
        if (myPropItem.newItem) {
            list.add(0, myPropItem);
        } else {
            list.add(myPropItem);
        }
    }

    private void itemsNewInFront(List<MyPropInfo> list, Set<Integer> itemsNew, Set<Integer> rpNew) {
        int nextInsertIndex = 0;
        for (Integer item : itemsNew) {
            Iterator<MyPropInfo> ite = list.iterator();
            MyPropInfo curInfo = null;
            while (ite.hasNext()) {
                MyPropInfo info = ite.next();
                if (!info.isRedPacket() && info.prop_id == item) {
                    curInfo = info;
                    ite.remove();
                    break;
                }
            }
            if (curInfo != null) {
                if (nextInsertIndex >= list.size()) {
                    list.add(curInfo);
                } else {
                    list.add(nextInsertIndex, curInfo);
                }
                TimeLogger.msg("next insert index: " + nextInsertIndex + ", info:" + curInfo.prop_id);
                nextInsertIndex++;
            }
        }
        for (Integer item : rpNew) {
            Iterator<MyPropInfo> ite = list.iterator();
            MyPropInfo curInfo = null;
            while (ite.hasNext()) {
                MyPropInfo info = ite.next();
                if (info.isRedPacket() && info.getRedPacketStuffId() == item) {
                    curInfo = info;
                    ite.remove();
                    break;
                }
            }
            if (curInfo != null) {
                if (nextInsertIndex >= list.size()) {
                    list.add(curInfo);
                } else {
                    list.add(nextInsertIndex, curInfo);
                }
                TimeLogger.msg("next insert index: " + nextInsertIndex + ", info:" + curInfo.prop_id);
                nextInsertIndex++;
            }
        }
    }

    private final IFilter<MyPropInfo> filter = new IFilter<MyPropInfo>() {
        @Override
        public boolean filter(MyPropInfo myPropInfo) {
            int id = myPropInfo.prop_id;
            return id != PropItem.PROP_ITEM_N_DRAW_BOARD &&
                    id != PropItem.PROP_ITEM_W_DRAW_BOARD &&
                    id != PropItem.PROP_ITEM_WHITE_DRAW_BOARD;
        }
    };
}
