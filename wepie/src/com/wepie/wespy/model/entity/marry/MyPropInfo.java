package com.wepie.wespy.model.entity.marry;

import androidx.annotation.NonNull;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.wepie.wespy.R;

import java.io.Serializable;

/**
 * Created by three on 15/9/28.
 */
public class MyPropInfo implements Serializable, RingListInterface {

    public int uid;

    public int prop_id;

    public int number;

    public int used_number;

    public long expired_time;

    public String name = "";

    public String media_url = "";

    public String simple_desc = "";

    public String label_icon = "";

    public int is_single_ring = 0;//0 :不是  1：是

    public int level;

    /**
     * 1 in use, else not
     */
    public int state = 0;

    /**
     * 与该字段相同 {@link PropItem#getType()}
     */
    public int type = -1;

    public boolean is_double_attributes_prop; // 是否是双属性道具
    private transient PropItem propItem;

    public boolean isSingleRing() {
        return is_single_ring == 1;
    }

    public boolean isRedPacket() {
        return type == PropItem.TYPE_RED_PACKET;
    }

    public int getRedPacketStuffId() {
        if (isRedPacket()) {
            return prop_id;
        } else {
            return 0;
        }
    }

    @NonNull
    public String getUseCountsLeftStr() {
        return ResUtil.getStr(R.string.prop_use_rounds_left_x, number);
    }


    /**
     * 是否是按次数计算的道具
     */
    public boolean isUseByCountsProp() {
        return prop_id == PropItem.PROP_ID_QUALIFY_DOUBLE_POINTS_NUMBER_CARD || prop_id == PropItem.PROP_ID_FAMILY_DOUBLE_POINTS_NUMBER_CARD;
    }

    public boolean isUsingEnterRoomAnim() {
        return type == PropItem.TYPE_VOICE_ENTER_ANIM && isUsing();
    }

    public boolean isUsingCarAnim() {
        return type == PropItem.TYPE_CAR_ANIM && isUsing();
    }

    public boolean isUsingJackarooChessboard() {
        return type == PropItem.TYPE_JACKAROO_BOARD && isUsing();
    }

    public boolean isUsingJackarooPiece() {
        return type == PropItem.TYPE_JACKAROO_PIECE && isUsing();
    }

    public boolean isUsingJackarooPlayCardAnimation() {
        return type == PropItem.TYPE_JACKAROO_PLAY_CARD_ANIMATION && isUsing();
    }

    public boolean isUsingJackarooKillAnimation() {
        return type == PropItem.TYPE_JACKAROO_KILL_ANIMATION && isUsing();
    }

    public boolean isUsingJackarooExchangeAnimation() {
        return type == PropItem.TYPE_JACKAROO_EXCHANGE_ANIMATION && isUsing();
    }

    @Override
    public int getPropId() {
        return prop_id;
    }

    @Override
    public String getPropImgUrl() {
        return media_url;
    }

    public void setInUse(boolean inUse) {
        state = inUse ? 1 : 0;
    }

    public boolean inUse() {
        return state == 1;
    }

    public @NonNull String getTimeLeftString() {
        if (expired_time > 0) {
            return TimeUtil.getTimeLeftString(expired_time);
        } else if (getPropItem().isVipItem()) {
            return ResUtil.getString(R.string.vip_exclusive_text);
        } else {
            return "";
        }
    }

    public boolean isUsing() {
        return state == 1;
    }

    public boolean isUsingChatBubble() {
        return type == PropItem.TYPE_CHAT_BUBBLE && isUsing();
    }

    private PropItem getPropItem() {
        if (propItem == null) {
            propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(prop_id);
        }
        if (propItem == null) {
            //no reached normally
            propItem = new PropItem();
        }
        return propItem;
    }

    public int getItemId() {
        if (getPropItem() != null) {
            return getPropItem().getItemId();
        }
        return -1;
    }
}
