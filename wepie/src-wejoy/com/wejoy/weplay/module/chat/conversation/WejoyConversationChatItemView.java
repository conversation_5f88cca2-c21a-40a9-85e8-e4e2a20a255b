package com.wejoy.weplay.module.chat.conversation;

import static com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt.SCENE_FOLLOW;
import static com.wepie.wespy.model.entity.GroupChatMsg.AT_ALL;
import static com.wepie.wespy.model.entity.GroupChatMsg.AT_ME;
import static com.wepie.wespy.model.entity.GroupChatMsg.AT_NO;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.SingleClickListener;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.decorate.NameTextView;
import com.huiwan.littlegame.cocos.CocosLaunchInfo;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.emoji.view.EmojiHelper;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.push.OpenNotificationDialogUtil;
import com.wepie.wespy.helper.push.WodiPushUtil;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.helper.shence.ShenceUtil;
import com.wepie.wespy.model.entity.ConversationInfo;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.entity.JumpCocosTeamInfo;
import com.wepie.wespy.model.entity.MatchState;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.group.GroupInfo;
import com.wepie.wespy.module.chat.MsgContentHelper;
import com.wepie.wespy.module.chat.conversation.GameState;
import com.wepie.wespy.module.chat.conversation.GameStateManager;
import com.wepie.wespy.module.chat.conversation.MatchStateManager;
import com.wepie.wespy.module.chat.ui.adapter.ConGroupHeadView;
import com.wepie.wespy.module.chat.ui.group.GroupHeadCompat;
import com.wepie.wespy.module.common.jump.JumpCocosTeamUtil;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.contact.detail.view.tags.TagUtil;
import com.wepie.wespy.module.marry.MarryUtil;
import com.wepie.wespy.utils.ShellPackageUtil;

/**
 * Created by three on 2018/2/1.
 */

public class WejoyConversationChatItemView extends LinearLayout {
    private final Context mContext;
    private RelativeLayout itemLay;
    private CustomCircleImageView personalHeadImg;
    private ConGroupHeadView groupHeadView;
    private TextView msgNumTx;
    private ImageView smallDotImg;
    private TextView timeTx;
    private NameTextView nameTx;
    private TextView conTag;
    private TextView msgContentTx;
    private View gameStateLay;
    private ImageView stateLockImg;
    private TextView stateTx;
    private ImageView notifyImage;
    private TextView groupTeamStatusTv;
    private TextView atMeTv;
    private ImageView arrowImv;
    private View topPadding;
    private boolean isFirstUpdate = true;
    private boolean isGroupTop;
    private View notificationLay;
    private ImageView sendFailIv;
    private TextView draftTv;

    public WejoyConversationChatItemView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    public WejoyConversationChatItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
        setOrientation(VERTICAL);
        LayoutInflater.from(mContext).inflate(R.layout.wejoy_convarsation_item_view, this);
        itemLay = findViewById(R.id.con_item_lay);
        atMeTv = findViewById(R.id.at_me_tv);
        sendFailIv = findViewById(R.id.msg_send_text_fail);
        draftTv = findViewById(R.id.draft_tv);
        personalHeadImg = findViewById(R.id.con_item_head_img);
        personalHeadImg.setDefaultHeadBorder();
        groupHeadView = findViewById(R.id.con_item_group_head);
        msgNumTx = findViewById(R.id.con_item_msg_num);
        smallDotImg = findViewById(R.id.con_item_small_dot);
        timeTx = findViewById(R.id.con_item_time);
        nameTx = findViewById(R.id.con_item_name);
        conTag = findViewById(R.id.con_tag);
        msgContentTx = findViewById(R.id.con_item_msg_content);
        gameStateLay = findViewById(R.id.con_item_game_state_lay);
        stateLockImg = findViewById(R.id.con_item_state_lock_ic);
        stateTx = findViewById(R.id.con_item_state_tx);
        notifyImage = findViewById(R.id.con_item_notify_img);
        groupTeamStatusTv = findViewById(R.id.group_team_status_tv);
        arrowImv = findViewById(R.id.con_item_state_arrow_ic);
        topPadding = findViewById(R.id.top_paddimg);
        notificationLay = findViewById(R.id.notification_open_lay);
        ImageView notificationCloseBtn = findViewById(R.id.close_lay_btn);

        notificationCloseBtn.setOnClickListener(v -> {
            OpenNotificationDialogUtil.isUserCloseChatNotifcationLay = true;
            notificationLay.setVisibility(GONE);
        });
        notificationLay.setOnClickListener(v -> DialogUtil.showOpenNotificationGuideDialog(getContext()));
    }

    public void updateConversation(final ConversationInfo info) {
        if (info.isPersonalChat()) {
            updatePersonalChat(info);
            groupTeamStatusTv.setVisibility(GONE);
            notifyImage.setVisibility(!info.isNotDisturb() ? View.GONE : View.VISIBLE);
        } else {
            GroupInfo groupInfo = info.groupInfo;
            if (groupInfo != null && groupInfo.isTeamMatching() && !ShellPackageUtil.hideInviteMsg()) {
                setTeamMatchingTv(groupInfo);
                notifyImage.setVisibility(GONE);
            } else {
                groupTeamStatusTv.setVisibility(GONE);
                notifyImage.setVisibility(info.groupInfo.isNotifyOn() ? View.GONE : View.VISIBLE);
            }

            if (isFirstUpdate || isGroupTop != info.isTop()) {
                if (info.isTop()) {
                    groupHeadView.setChildBackground(Color.parseColor("#f3f3f3"));
                } else {
                    groupHeadView.setChildBackground(Color.parseColor("#ffffff"));
                }
                isGroupTop = info.isTop();
            }
            isFirstUpdate = false;

            updateGroupChat(info);
        }

        TagUtil.updateTagInfo(TagUtil.getTagType(info.getPersonalChatUid(), info), conTag);
        setReddot(info);
        timeTx.setText(info.getTimeString());
        itemLay.setBackgroundResource(info.isTop() ? R.drawable.room_list_stick_sel : R.drawable.msg_center_item_simple_bg);
    }

    private void setTeamMatchingTv(GroupInfo groupInfo) {
        groupTeamStatusTv.setVisibility(VISIBLE);
        groupTeamStatusTv.setText(groupInfo.getTeamMatchingStatus());
    }

    private void updateGroupChat(final ConversationInfo info) {
        updateGroupHead(info);
        final int isAtMe = info.isAtMe();
        setAtMeTV(isAtMe);
        showFailedIv(info.last_self_msg_status == GroupChatMsg.STATUS_FAIL);
        if (!TextUtils.isEmpty(info.draft) || !TextUtils.isEmpty(info.ref_mid)) {
            draftTv.setVisibility(View.VISIBLE);
        } else {
            draftTv.setVisibility(View.GONE);
        }

        MsgContentHelper.INSTANCE.getContent(info, content -> {
            EmojiHelper.parseEmojis(getContext(), msgContentTx, showMsgCount(info) + content, 14);
            return null;
        });

        gameStateLay.setVisibility(View.GONE);
    }

    private void updatePersonalChat(final ConversationInfo info) {
        updatePersonalHead(info);
        setAtMeTV(AT_NO);

        showFailedIv(info.last_self_msg_status == WPMessage.STATUS_FAIL);
        if (!TextUtils.isEmpty(info.draft) || !TextUtils.isEmpty(info.ref_mid)) {
            draftTv.setVisibility(View.VISIBLE);
        } else {
            draftTv.setVisibility(View.GONE);
        }
        msgContentTx.setTag(info);
        MsgContentHelper.INSTANCE.getContent(info, content -> {
            Object tag = msgContentTx.getTag();
            if (info == tag) {
                EmojiHelper.parseEmojis(getContext(), msgContentTx, showMsgCount(info) + content, 14);
            }
            return null;
        });

        GameState gameState = GameStateManager.getInstance().getGameState(info.target_uid);
        MatchState matchState = MatchStateManager.getInstance().getMatchState(info.target_uid);
        initGameState(gameState, matchState, info.target_uid);
        notifyImage.setVisibility(View.GONE);
    }

    private void setAtMeTV(int show) {
        if (show == AT_ME) {
            atMeTv.setVisibility(VISIBLE);
            atMeTv.setText(ResUtil.getStr(R.string.convarsation_item_view_4));
        } else if (show == AT_ALL) {
            atMeTv.setVisibility(VISIBLE);
            atMeTv.setText(ResUtil.getStr(R.string.convarsation_item_view_all_4));
        } else {
            atMeTv.setVisibility(GONE);
        }
    }

    private void showFailedIv(boolean show) {
        sendFailIv.setVisibility(show ? VISIBLE : GONE);
    }

    private void initGameState(final GameState gameState, final MatchState matchState, final int target_uid) {
        if (gameState != null) {
            String gameNameText = ConfigHelper.getInstance().getShortGameName(gameState.game_type);
            if (TextUtils.isEmpty(gameNameText)) {
                gameStateLay.setVisibility(View.GONE);
            } else {
                gameStateLay.setVisibility(View.VISIBLE);
                if (GameConfig.isJackarooVip(gameState.game_type, gameState.gameMode)) {
                    stateTx.setText(R.string.jackaroo_vip_room_playing);
                } else {
                    String stateText = ResUtil.getStr(R.string.tab_msg_conversation_item_content_playing, gameNameText);
                    stateTx.setText(stateText);
                }


                gameStateLay.setBackgroundResource(R.drawable.shape_playing_border);
                stateTx.setTextColor(Color.parseColor("#44CE5E"));
                arrowImv.setImageResource(R.drawable.green_arrow_game_state);
                stateLockImg.setVisibility(gameState.lock || MarryUtil.showCpRoomLock(target_uid, gameState.game_type) ? View.VISIBLE : View.GONE);
                gameStateLay.setOnClickListener(new SingleClickListener(1000) {
                    @Override
                    public void onClickInternal(@NonNull View v) {
                        gotoRoom(gameState.rid, target_uid, gameState.game_type);
                        ShenceUtil.followClickReport(TrackScreenName.CHAT_CONVERSATION, gameState.game_type, target_uid);
                    }
                });
            }
        } else if (matchState != null) {
            String gameNameText = ConfigHelper.getInstance().getShortGameName(matchState.game_type);
            if (TextUtils.isEmpty(gameNameText)) {
                gameStateLay.setVisibility(View.GONE);
            } else {
                gameStateLay.setVisibility(View.VISIBLE);
                if (GameConfig.isJackarooVip(matchState.game_type, matchState.gameMode)) {
                    stateTx.setText(R.string.jackaroo_vip_team_waiting);
                } else {
                    String stateText = ResUtil.getStr(R.string.game_status_teaming_up, gameNameText);
                    stateTx.setText(stateText);
                }

                gameStateLay.setBackgroundResource(R.drawable.shape_grouping_border);
                stateTx.setTextColor(Color.parseColor("#FF9D2F"));
                arrowImv.setImageResource(R.drawable.yellow_arrow_game_state);


                stateLockImg.setVisibility(View.GONE);
                gameStateLay.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        String referScreenName = TrackButtonName.FOLLOW;
                        if (GameConfig.isJackarooVip(matchState.game_type, matchState.gameMode)) {
                            referScreenName = TrackButtonName.FOLLOW_VIP_GAME_ROOM;
                        }
                        JumpCocosTeamInfo teamInfo = JumpCocosTeamInfo.build(mContext)
                                .setTid(matchState.tid).setGameType(matchState.game_type)
                                .setIsVip(GameConfig.isJackarooVip(matchState.game_type, matchState.gameMode))
                                .setTargetUid(target_uid).setScene(SCENE_FOLLOW)
                                .setEnterVipReferScreenName(referScreenName);
                        JumpCocosTeamUtil.jumpToCocosGame(teamInfo, CocosLaunchInfo.ENTER_MODE_ENTER_TO_COCOS);
                    }
                });
            }
        } else {
            gameStateLay.setVisibility(View.GONE);
        }
    }

    private void gotoRoom(int rid, int targetUid, int game_type) {
        EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(mContext, rid, game_type).setFollowUid(targetUid).setSourceView(personalHeadImg).setSource(TrackSource.MSG_LIST);
        JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
    }

    private void setReddot(ConversationInfo info) {
        RedDotUtil.setMsgNum(msgNumTx, info.unread_num);
        if ((info.isGroup() && !info.groupInfo.isNotifyOn()) || (!info.isGroup() && info.isNotDisturb())) {
            msgNumTx.setVisibility(View.GONE);//群消息通知关闭时不显示数字
            smallDotImg.setVisibility(info.unread_num > 0 ? View.VISIBLE : View.GONE);
        } else {
            smallDotImg.setVisibility(View.GONE);
        }
    }

    private String showMsgCount(ConversationInfo info) {
        String res = "";
        if (info.isGroup() && !info.groupInfo.isNotifyOn() && info.unread_num > 1 && info.isAtMe() == 0) {
            String s = String.valueOf(info.unread_num);
            if (info.unread_num > 99) {
                s = ResUtil.getStr(R.string.c_x_number, "99+");
            }
            res = ResUtil.getStr(R.string.unread_msg_count, s);
        } else if (!info.isGroup() && info.isNotDisturb() && info.unread_num > 1) {
            String s = String.valueOf(info.unread_num);
            if (info.unread_num > 99) {
                s = ResUtil.getStr(R.string.c_x_number, "99+");
            }
            res = ResUtil.getStr(R.string.unread_msg_count, s);
        }
        return res;
    }

    private void updatePersonalHead(ConversationInfo info) {
        final int uid = info.getPersonalChatUid();
        groupHeadView.setVisibility(View.GONE);
        personalHeadImg.setVisibility(View.VISIBLE);
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                HeadImageLoader.loadCircleHeadImage(userInfo.headimgurl, personalHeadImg);
                nameTx.setUserName(userInfo);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
        ViewUtil.setTextLeftDrawable(nameTx, null);
    }

    private void updateGroupHead(ConversationInfo info) {
        final GroupInfo groupInfo = info.groupInfo;
        groupHeadView.setVisibility(View.VISIBLE);
        personalHeadImg.setVisibility(View.INVISIBLE);
        GroupHeadCompat.INSTANCE.showHeadIcon(groupHeadView, groupInfo);
        nameTx.setText(groupInfo.name);
        nameTx.setVipLevel(0);
    }

    public void setTopBorderShow(boolean show) {
        if (show) {
            topPadding.setVisibility(VISIBLE);
            if (!WodiPushUtil.isNotification(getContext()) && !OpenNotificationDialogUtil.isUserCloseChatNotifcationLay) {
                notificationLay.setVisibility(VISIBLE);
            } else {
                notificationLay.setVisibility(GONE);
            }
        } else {
            topPadding.setVisibility(GONE);
            notificationLay.setVisibility(GONE);
        }
    }
}
