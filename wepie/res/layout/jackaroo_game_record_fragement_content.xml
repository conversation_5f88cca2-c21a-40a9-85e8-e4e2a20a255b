<?xml version="1.0" encoding="utf-8"?>
<com.huiwan.widget.StickTopLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/game_record_host"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:collapseViewId="@id/game_record_head_lay"
    app:scrollViewId="@id/jackaroo_game_record_vp"
    tools:background="@color/black">

    <LinearLayout
        android:id="@+id/jackaroo_game_record_top_iv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/game_record_head_lay"
            android:layout_width="match_parent"
            android:layout_height="104dp"
            android:layout_marginHorizontal="16dp"
            android:paddingVertical="8dp">

            <View
                android:id="@+id/game_record_state_bg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/shape_f7f8fa_corner8" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <View
                    android:id="@+id/line1"
                    android:layout_width="1dp"
                    android:layout_height="27dp"
                    android:background="#D9D9D9"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/line2"
                    app:layout_constraintHorizontal_chainStyle="spread"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/line2"
                    android:layout_width="1dp"
                    android:layout_height="27dp"
                    android:background="#D9D9D9"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/line1"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/jackaroo_game_record_highest_qualifying_lay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toTopOf="@+id/jackaroo_game_record_highest_qualifying_title_tv"
                    app:layout_constraintEnd_toEndOf="@id/line1"
                    app:layout_constraintStart_toStartOf="parent">

                    <LinearLayout
                        android:id="@+id/jackaroo_game_record_highest_qualifying_grade_king_lay"
                        android:layout_width="wrap_content"
                        android:layout_height="16dp"
                        android:layout_marginStart="-10dp"
                        android:background="@drawable/jackaroo_game_record_qualifying_start_bg"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/jackaroo_game_record_highest_qualifying_iv"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginStart="12dp"
                            android:src="@drawable/qualifying_grade_share_star" />

                        <TextView
                            android:id="@+id/jackaroo_game_record_highest_qualifying_grade_king_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:text="999"
                            android:textColor="@color/color_ffed53"
                            android:textFontWeight="600"
                            android:textSize="10dp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/jackaroo_game_record_highest_qualifying_iv"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/jackaroo_game_record_no_qualifying"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/jackaroo_game_record_highest_qualifying_grade_king_lay"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/jackaroo_game_record_highest_qualifying_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:gravity="center"
                    android:text="@string/jackaroo_game_record_highest_qualifying"
                    android:textColor="@color/color_text_primary_ex"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="@+id/jackaroo_game_record_total_round_title_tv"
                    app:layout_constraintEnd_toEndOf="@id/line1"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="Total Rounds" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/jackaroo_game_record_total_round_num_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:gravity="center"
                    android:text="***"
                    android:textColor="@color/text_primary"
                    android:textFontWeight="700"
                    android:textSize="18dp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/jackaroo_game_record_total_round_title_tv"
                    app:layout_constraintEnd_toEndOf="@id/line2"
                    app:layout_constraintStart_toStartOf="@+id/line1"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/jackaroo_game_record_total_round_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:gravity="center"
                    android:text="@string/jackaroo_game_record_total_round"
                    android:textColor="@color/color_text_primary_ex"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/line2"
                    app:layout_constraintStart_toStartOf="@+id/line1"
                    app:layout_constraintTop_toBottomOf="@+id/jackaroo_game_record_total_round_num_tv"
                    tools:text="Total Rounds" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/jackaroo_game_record_win_rate_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:gravity="center"
                    android:text="***"
                    android:textColor="@color/text_primary"
                    android:textFontWeight="700"
                    android:textSize="18dp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/jackaroo_game_record_win_rate_title_tv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/line2"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/jackaroo_game_record_win_rate_title_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:gravity="center"
                    android:text="@string/jackaroo_game_record_win_rate"
                    android:textColor="@color/color_text_primary_ex"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/line2"
                    app:layout_constraintTop_toBottomOf="@+id/jackaroo_game_record_win_rate_tv" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>

        <com.huiwan.base.ui.empty.HWUIEmptyView
            android:id="@+id/record_hide_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="58dp"
            android:visibility="gone"
            app:icon_type="base_empty_nothing"
            app:text="@string/user_detail_hide_sorce_tips" />

        <FrameLayout
            android:id="@+id/game_record_tab_container_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.huiwan.widget.WejoyLabelLayout
                android:id="@+id/jackaroo_game_record_tab_view"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:layout_gravity="start|center_vertical"
                android:paddingVertical="5dp"
                app:tabIndicator="@null"
                app:tabMode="scrollable"
                app:tabPaddingEnd="16dp"
                app:tabPaddingStart="16dp"
                app:tabRippleColor="@null" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/jackaroo_game_record_help_iv"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="16dp"
                android:padding="2dp"
                app:srcCompat="@drawable/ic_help_21"
                app:tint="#999CB4" />
        </FrameLayout>

    </LinearLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/jackaroo_game_record_vp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

</com.huiwan.widget.StickTopLinearLayout>