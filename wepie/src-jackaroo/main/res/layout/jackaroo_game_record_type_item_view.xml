<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="110dp"
    android:background="@color/normal_light_grey">

    <ImageView
        android:id="@+id/jackaroo_game_record_type_item_bg"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:scaleX="@integer/image_scale_x"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/jackaroo_game_record_type_item_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/jackaroo_game_mode_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="20dp"
        android:includeFontPadding="false"
        android:lineHeight="20dp"
        android:textColor="@color/black"
        android:textFontWeight="700"
        android:textSize="20dp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/jackaroo_game_record_total_round_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/jackaroo_game_mode_tv">

        <TextView
            android:id="@+id/jackaroo_game_record_item_total_round_num"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:includeFontPadding="false"
            android:textColor="@color/color_text_primary"
            android:textFontWeight="700"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:includeFontPadding="false"
            android:text="@string/jackaroo_game_record_total_round"
            android:textColor="@color/color_text_tertiary"
            android:textSize="10dp" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="20dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/jackaroo_game_record_total_round_lay">

        <TextView
            android:id="@+id/jackaroo_game_record_item_win_rate_tv"
            android:layout_width="wrap_content"
            android:layout_height="18dp"
            android:includeFontPadding="false"
            android:textColor="@color/color_text_primary"
            android:textFontWeight="700"
            android:textSize="18dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:includeFontPadding="false"
            android:text="@string/jackaroo_game_record_win_rate"
            android:textColor="@color/color_text_tertiary"
            android:textSize="10dp" />
    </androidx.appcompat.widget.LinearLayoutCompat>


</androidx.constraintlayout.widget.ConstraintLayout>