<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="60dp"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/prop_iv"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:layout_gravity="center_horizontal"
        android:padding="5dp"
        app:srcCompat="@drawable/jackaroo_collection_prop_empty" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/prop_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:maxWidth="60dp"
        android:maxLines="1"
        android:textColor="@color/color_text_secondary"
        app:sizeAndFont="Body2|tajawal" />
</LinearLayout>