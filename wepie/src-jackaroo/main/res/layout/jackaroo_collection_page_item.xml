<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:background="@drawable/shape_f7f8fa_corner8"
    android:padding="12dp">

    <com.wejoy.jackaroo.record.CollectionGradeView
        android:id="@+id/collection_grade_view"
        android:layout_width="0dp"
        android:layout_height="32dp"
        app:layout_constraintEnd_toStartOf="@id/arrow_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/arrow_iv"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:layout_marginTop="2dp"
        app:layout_constraintBottom_toBottomOf="@id/collection_grade_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/collection_grade_view"
        app:srcCompat="@drawable/ic_arrow_right"
        app:tint="@color/color_text_tertiary" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/prop_list"
        android:layout_width="0dp"
        android:layout_height="80dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>