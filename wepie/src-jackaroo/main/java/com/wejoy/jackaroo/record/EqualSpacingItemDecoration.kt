package com.wejoy.jackaroo.record

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

/**
 * 两边贴着RecyclerView,中间间距等分
 */
class EqualSpacingItemDecoration(
    private val itemWidth: Int
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val itemCount = state.itemCount
        val totalWidth = parent.width

        if (itemCount <= 1) return


        val totalItemWidth = itemWidth * itemCount
        val space = (totalWidth - totalItemWidth) / (itemCount - 1f)

        outRect.top = 0
        outRect.bottom = 0

        outRect.left = if (position == 0) 0 else (space / 2).toInt()
        outRect.right = if (position == itemCount - 1) 0 else (space / 2).toInt()
    }
}
