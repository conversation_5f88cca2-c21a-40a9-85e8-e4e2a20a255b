package com.wejoy.jackaroo.record

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.huiwan.base.util.ToastUtil
import com.huiwan.constants.IntentConfig
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.databinding.JackarooGameRecordFragementContentBinding
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class GameRecordFragment : Fragment(), IChildScrollStateProvider {
    private lateinit var adapter: FragmentStateAdapter
    private val viewModel: JackarooGameCareerViewModel by lazy { ViewModelProvider(requireActivity())[JackarooGameCareerViewModel::class.java] }
    private lateinit var binding: JackarooGameRecordFragementContentBinding
    private val jackarooGameRecordVp: ViewPager2 by lazy { binding.jackarooGameRecordVp }
    private var index = 0
    private var gameType = 0
    private var forShare = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return JackarooGameRecordFragementContentBinding.inflate(
            inflater,
            container,
            false
        ).let {
            binding = it
            it.root
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        parseArg()
        initData()
    }

    private fun parseArg() {
        index = arguments?.getInt(PARAM_INDEX) ?: 0
        gameType = arguments?.getInt(PARAM_GAME_TYPE) ?: 0
        forShare = arguments?.getBoolean(PARAM_FOR_SHARE) ?: false
    }


    private fun initView() {
        adapter = object : FragmentStateAdapter(this) {
            override fun createFragment(position: Int): Fragment {
                if (position == 0) {
                    return TotalStatFragment().apply {
                        val arg = Bundle()
                        arg.putInt(IntentConfig.GAME_TYPE, gameType)
                        arg.putBoolean(TotalStatFragment.PARAM_FOR_SHARE, forShare)
                        arguments = arg
                    }
                }
                return JackarooRecentStatFragment()
            }

            override fun getItemCount(): Int = gameRecordTitleList.size
        }
        jackarooGameRecordVp.adapter = adapter
        jackarooGameRecordVp.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                binding.jackarooGameRecordHelpIv.isVisible = position == 0
                if (!forShare) {
                    viewModel.notifyEvent(JackarooGameCareerEvent.SwitchGameGameRecordTab(position))
                }
                //由于最近战绩页面没有滑动冲突，然后由于总战绩页面会修改disallowIntercept状态，因此这里 需要重置 祖先节点的 disallowIntercept状态
                if (position == 1) {
                    jackarooGameRecordVp.requestDisallowInterceptTouchEvent(false)
                }
            }
        })
        binding.recordHideView.setType(R.drawable.base_empty_no_game)
        binding.jackarooGameRecordHelpIv.setOnClickListener {
            ToastUtil.show(R.string.jackaroo_game_record_tips)
        }
        changeViewVisibilityByIsUserHideGameRecord(viewModel.shouldHideGameRecord)
        initGameRecordLabelView(binding.root)
        binding.jackarooGameRecordTabView.setupViewPager(jackarooGameRecordVp)
    }

    private fun changeViewVisibilityByIsUserHideGameRecord(isUserHideGameRecord: Boolean) {
        if (isUserHideGameRecord) {
            binding.gameRecordTabContainerLay.isVisible = false
            binding.recordHideView.isVisible = true
            binding.jackarooGameRecordVp.isVisible = false
        } else {
            binding.gameRecordTabContainerLay.isVisible = true
            binding.recordHideView.isVisible = false
            binding.jackarooGameRecordVp.isVisible = true
        }
    }

    private fun initData() {
        binding.jackarooGameRecordVp.setCurrentItem(index, false)
        viewModel.recordInfoLiveData.observe(this.viewLifecycleOwner) {
            binding.bindRecordSummary(
                it.rankStat ?: JackarooGameInfo(),
                viewModel.shouldHideGameRecord
            )
        }
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.outAppbarState.collectLatest {
                    binding.root.targetAppBarCollapse = it.isCollapsed
                    binding.root.targetAppBarTotalExpand = it.isTotalExpanded
                }
            }
        }

    }

    override fun canHorizontalScroll(direction: Int): Boolean {
        return jackarooGameRecordVp.canScrollHorizontally(direction)
    }

    override fun onTouchEvent(e: MotionEvent) {
        HLog.d("jackarooGameRecordVp", "onTouchEvent:${e.action}")
        jackarooGameRecordVp[0].onTouchEvent(e)
    }

    companion object {
        const val PARAM_INDEX = "index"
        const val PARAM_GAME_TYPE = "game_Type"

        //构造页面是否是用于分享
        const val PARAM_FOR_SHARE = "for_share"

        @JvmStatic
        fun newInstance(index: Int = 0, subIndex: Int, forShare: Boolean) =
            GameRecordFragment().apply {
                arguments = Bundle().apply {
                    putInt(PARAM_INDEX, index)
                    putInt(PARAM_GAME_TYPE, subIndex)
                    putBoolean(PARAM_FOR_SHARE, forShare)
                }
            }

    }
}