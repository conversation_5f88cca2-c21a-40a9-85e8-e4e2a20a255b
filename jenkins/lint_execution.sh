#!/bin/bash

WORKSPACE="$1"
branch="$2"
channel="$3"
job_id="$4"
# 获取.gradle目录路径
GRADLE_DIR="$WORKSPACE/wejoy-android/.gradle"
# 定义记录lint执行时间的文件
LINT_RECORD_FILE="$GRADLE_DIR/last_lint_execution_${branch}"
echo "$LINT_RECORD_FILE"
# 检查.gradle目录是否存在，不存在则创建
if [ ! -d "$GRADLE_DIR" ]; then
    mkdir -p "$GRADLE_DIR"
fi

# 获取当前日期（只取年月日）
CURRENT_DATE=$(date +"%Y-%m-%d")
codeDir=$WORKSPACE/wejoy-android

## 检查记录文件是否存在
#if [ -f "$LINT_RECORD_FILE" ]; then
#    # 读取上次执行lint的日期
#    LAST_EXECUTION_DATE=$(cat "$LINT_RECORD_FILE")
#
#    # 比较日期，如果已经是今天，则不执行
#    if [ "$CURRENT_DATE" = "$LAST_EXECUTION_DATE" ]; then
#        echo "Lint检查今天已经执行过，跳过执行"
#        exit 0
#    fi
#fi
#
## 执行lint检查
#echo "执行lint检查"
#cd $codeDir
#./gradlew ":wepie:lint${channel}Debug"
## 记录执行时间
#echo "$CURRENT_DATE" > "$LINT_RECORD_FILE"
#echo "Lint检查执行完成"
LINT_REPORT_FILE="$codeDir/wepie/build/reports/lint-results-${channel}Debug.html"
if [ -f "$LINT_REPORT_FILE" ]; then
  echo "Lint report generated successfully!"
  NODE_NAME=$(basename "$WORKSPACE")
  url="https://jk.wepieoa.com/view/WePlay%E5%AE%A2%E6%88%B7%E7%AB%AF/job/${NODE_NAME}/${job_id}/artifact/wejoy-android/wepie/build/reports/lint-results-${channel}Debug.html"
  echo "Report URL: $url"

  # 飞书通知暂时注释，需要检查webhook配置
  # p_content='{
  #     "msg_type":"text",
  #     "content":
  #     {
  #         "text": "Lint check completed successfully"
  #     }
  #   }'
  # curl -4 'https://open.feishu.cn/open-apis/bot/v2/hook/a8fa591f-7845-43a7-9906-3be63f1b5c04' -X POST -H "Content-Type: application/json" -d "$p_content"
fi